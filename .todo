# Codebase Audit Checklist

## Discovery Phase
- [ ] Map all entry points and API routes
- [ ] Catalog AI provider modules and their usages
- [ ] Identify duplicated / legacy provider implementations
- [ ] Scan for unused or deprecated dependencies

## Documentation Cross-Reference
- [ ] Cross-check OpenAI usages with https://platform.openai.com/docs/latest
- [ ] Cross-check Google GenAI usages with official @google/genai docs
- [ ] Verify NextAuth OAuth 2.0 configuration matches Twitter docs
- [ ] Confirm Prisma vector usage compatible with pgvector extension

## Simplification Targets (first pass)
- [ ] Remove legacy `lib/ai/providers/*` after migrating to `lib/ai/providers.ts`
- [ ] Update all imports to use new unified provider factory
- [ ] Refactor `app/api/ai/models/route.ts` to call unified factory
- [ ] Trim duplicated validation logic in Google provider

## Feature Auditing
### AI Providers
- [ ] OpenAIProvider method parity with docs (list, chat, embeddings)
- [ ] GoogleGenAIProvider method parity with docs (generateContent, embedContent, list)
- [ ] Streaming handling conforms to SDK examples

### Embeddings Service
- [ ] Replace raw `vector` SQL with Prisma pgvector support once released
- [ ] Ensure type-safety (remove `any`, undefined checks)

### Model Selector Component
- [ ] Ensure live model fetch endpoint path consistency
- [ ] Confirm similarity search works for >1k models without perf issues

## Testing
- [ ] Unit tests for provider factory (mocked network)
- [ ] Integration test for `/api/ai/models` route
- [ ] Component test for EnhancedModelSelector search logic

## Reporting
- [ ] Generate audit report with recommendations 