{"name": "xtasker", "version": "0.1.0", "private": true, "engines": {"node": ">=24.0.0", "npm": ">=10.0.0", "bun": ">=1.0.0"}, "scripts": {"dev": "dotenv -e .env -- next dev --turbo --port ${PORT:-3030}", "build": "next build", "build:standalone": "STANDALONE=true next build && cp -r .next/static .next/standalone/.next/ && cp -r public .next/standalone/", "start": "dotenv -e .env -- next start --hostname 0.0.0.0 --port ${PORT:-3030}", "start:standalone": "dotenv -e .env -- node .next/standalone/server.js", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@google/genai": "^1.5.0", "@hookform/resolvers": "^3.10.0", "@neondatabase/serverless": "^0.9.5", "@next/env": "^15.3.3", "@next/swc-wasm-nodejs": "14.1.3", "@prisma/adapter-neon": "^5.22.0", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.15.31", "@types/node-schedule": "^2.1.7", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@uploadthing/react": "^7.3.1", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "bun": "^1.2.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookie-parser": "^1.4.7", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "dotenv-cli": "^7.4.4", "embla-carousel-react": "^8.6.0", "emoji-mart": "^5.6.0", "eslint": "^8.57.1", "eslint-config-next": "^14.2.30", "express-session": "^1.18.1", "framer-motion": "^12.17.3", "google-auth-library": "^9.15.1", "input-otp": "^1.4.2", "iron-session": "^8.0.4", "jsonwebtoken": "^9.0.2", "limiter": "^3.0.0", "lucide-react": "^0.446.0", "next": "^14.2.30", "next-auth": "^4.24.11", "next-connect": "^1.0.0", "next-themes": "^0.3.0", "node-schedule": "^2.1.1", "openai": "^4.104.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "passport-twitter": "^1.0.4", "passport-twitter-oauth2": "^2.1.1", "postcss": "^8.5.5", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-masonry-css": "^1.0.16", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.3", "sonner": "^1.7.4", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "twitter-api-v2": "^1.23.2", "typescript": "^5.8.3", "uploadthing": "^7.7.2", "vaul": "^0.9.9", "zod": "^3.25.63"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-local": "^1.0.38", "@types/passport-twitter": "^1.0.40", "prisma": "^5.22.0", "tsx": "^4.20.2"}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}