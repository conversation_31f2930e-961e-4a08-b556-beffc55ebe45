import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { validateAuth } from './lib/middleware/auth';

// Initialize server components only on server side
if (typeof window === 'undefined') {
  import('./lib/server-init').catch(console.error);
}

// Paths that don't require authentication
const publicPaths = [
  // Auth pages
  '/login',
  '/signup',
  '/login/passwordreset',
  '/login/passwordreset/newpassword',
  '/signup/verification',

  // Auth API endpoints
  '/api/auth/login',
  '/api/auth/logout',
  '/api/auth/register',
  '/api/auth/me',
  '/api/auth/password-reset',
  '/api/auth/password-reset/confirm',

  // NextAuth routes
  '/api/auth',

  // OAuth routes (legacy - keeping for compatibility)
  '/api/auth/google',
  '/api/auth/google/callback',
  '/api/auth/twitter',
  '/api/auth/twitter/callback',

  // Cron jobs and webhooks
  '/api/cron',
  '/api/uploadthing',

  // Public assets
  '/favicon.ico',
  '/_next',
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the path is public
  if (publicPaths.some(path => pathname === path || pathname.startsWith(path))) {
    return NextResponse.next();
  }

  // API routes starting with /api that aren't in publicPaths require auth
  if (pathname.startsWith('/api')) {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      );
    }
    return NextResponse.next();
  }

  // Check authentication for all other routes (dashboard, etc.)
  const user = await validateAuth(request);
  if (!user) {
    // Redirect to login page with the original URL as redirect parameter
    const redirectUrl = new URL('/login', request.url);
    redirectUrl.searchParams.set('redirect', encodeURIComponent(pathname));
    return NextResponse.redirect(redirectUrl);
  }

  return NextResponse.next();
}

// Configure paths that should be matched by middleware
export const config = {
  matcher: [
    // Match all request paths except static files and images
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};