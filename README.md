# 🚀 xsche - AI-Powered Twitter/X Management Platform

**Status**: 89% Complete - Authentication Refactor in Progress
**Next.js 14** | **TypeScript** | **PostgreSQL** | **AI Integration** | **Serverless Ready**

## 📋 Overview

xsche is a comprehensive AI-powered Twitter/X management platform built as a unified Next.js full-stack application. It enables users to compose, schedule, and publish tweets with intelligent bot personas and semantic knowledge base capabilities.

## ✨ Features

### 🔐 Authentication & Security
- ✅ Multi-provider OAuth (Google, Twitter, Email/Password)
- 🔄 **In Progress**: Migrating from Passport.js to NextAuth.js for serverless compatibility
- ✅ Encrypted API key storage
- ✅ Secure session management

### 🤖 AI Integration
- ✅ OpenAI and Google Generative AI support
- ✅ Custom model selection for different tasks
- ✅ Intelligent bot personas with personality files
- ✅ AI-powered content generation

### 📝 Tweet Management
- ✅ Rich text composer with character counting
- ✅ Media upload via UploadThing
- ✅ Emoji picker integration
- ✅ Advanced scheduling with automated publishing
- ✅ Cron-based tweet automation

### 🧠 Knowledge Base (Tweet Brain)
- ✅ Vector embeddings with pgvector
- ✅ Semantic search functionality
- ✅ Category-based organization
- ✅ Masonry layout display

### 🎨 Modern UI/UX
- ✅ Responsive dashboard with dark/light themes
- ✅ shadcn/ui component library
- ✅ Mobile-friendly navigation
- ✅ Real-time status indicators

## 🛠️ Technology Stack

### Core Framework
- **Next.js 14.2.3** with App Router
- **TypeScript** for type safety
- **Bun** package manager (Node.js 24+)

### Database & ORM
- **PostgreSQL** with pgvector extension
- **Prisma 5.11.0** with Neon adapter
- **Vector embeddings** for semantic search

### UI & Styling
- **Tailwind CSS** for styling
- **shadcn/ui** component library
- **Lucide React** for icons
- **next-themes** for theme management

### Authentication (Migrating)
- 🔄 **NextAuth.js** (replacing Passport.js)
- 🔄 **iron-session** for stateless sessions
- 🔄 **next-connect** for API middleware

### AI & Integrations
- **OpenAI SDK** for GPT models
- **Google Generative AI** (@google/genai)
- **UploadThing** for file uploads
- **Twitter API v2** for publishing

## 🚧 Current Development Status

### ✅ Completed (33/37 tasks)
- Core authentication system
- AI provider integrations
- Bot persona management
- Tweet composition and scheduling
- Knowledge base with semantic search
- Dashboard UI and navigation
- User profile management

### 🔄 In Progress
- **Task 32**: Fixing build configuration issues
- **Tasks 38-45**: Authentication system refactor

### 📋 Next Phase
1. **Authentication Refactor** - Replace Passport.js with NextAuth.js
2. **Build Configuration** - Resolve webpack issues with server dependencies
3. **Testing** - Comprehensive end-to-end testing
4. **Production Deployment** - Deploy to production environment

## 🏃‍♂️ Quick Start

### Prerequisites
- **Node.js 24+**
- **Bun** package manager
- **PostgreSQL** with pgvector extension

### Development Setup

```bash
# Clone the repository
git clone <repository-url>
cd xsche

# Install dependencies
bun install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Set up the database
bun run db:push
bun run db:generate

# Start development server
bun dev
```

### Environment Variables

```bash
# Database
DATABASE_URL="postgresql://..."
DIRECT_URL="postgresql://..."

# Authentication (NextAuth.js)
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
TWITTER_API_KEY="your-twitter-api-key"
TWITTER_API_SECRET="your-twitter-api-secret"

# AI Providers (User-provided via UI)
# Users input their own OpenAI and Google AI keys through the dashboard
```

## 📁 Project Structure

```
xsche/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── (main)/            # Dashboard pages
│   │   └── dashboard/     # Main application sections
│   └── api/               # API routes (serverless functions)
├── components/            # React components
│   ├── ui/                # shadcn/ui components
│   ├── dashboard/         # Dashboard components
│   ├── tweet/             # Tweet composition
│   ├── brain/             # Knowledge base
│   └── bots/              # Bot management
├── lib/                   # Utility libraries
│   ├── ai/                # AI provider integrations
│   ├── auth.ts            # Authentication utilities
│   ├── prisma.ts          # Database client
│   └── tweets/            # Tweet publishing logic
├── prisma/                # Database schema and migrations
├── docs/                  # Documentation
└── tasks/                 # Task management (Task Master)
```

## 🔧 Development Workflow

This project uses **Task Master** for development workflow management:

```bash
# View current tasks
task-master list

# Get next task to work on
task-master next

# Update task status
task-master set-status --id=32 --status=in-progress

# Add new tasks
task-master add-task --prompt="Description of new task"
```

## 🚨 Known Issues & Current Work

### Build Configuration Issues
- **xmldom dependencies** causing client-side bundling errors
- **Passport.js** incompatibility with serverless functions
- **React context** hydration issues

### Solution in Progress
- Replacing Passport.js with NextAuth.js for serverless compatibility
- Implementing iron-session for stateless session management
- Using next-connect for proper API middleware handling

## 🧪 Testing

```bash
# Run all tests
bun test

# Run specific test suites
bun test:unit
bun test:integration
bun test:e2e

# Test build
bun build
```

## 📦 Deployment

### Development
```bash
npm run dev
# Application runs on http://localhost:3030
```

### Production (Standalone - Recommended)
```bash
# Build for production with standalone output
npm run build

# Start standalone server
npm run start
# Uses: node .next/standalone/server.js
```

### Alternative: Traditional Next.js Server
```bash
# Build for production
npm run build

# Start with next start (only if standalone disabled)
npm run start:next
```

### Serverless Deployment
```bash
# Vercel/Netlify automatically handle standalone builds
vercel deploy
# or
netlify deploy
```

### Environment Configuration
- **Development**: `http://localhost:3000`
- **Production**: `https://tasker.violetmethods.com`
- **Port**: 3030 for nginx routing

## 📚 Documentation

- [`docs/current-workspace-state.md`](docs/current-workspace-state.md) - Current project status
- [`docs/proj.md`](docs/proj.md) - Original project requirements
- [`docs/technical-architecture.md`](docs/technical-architecture.md) - Technical architecture
- [`docs/url-configuration.md`](docs/url-configuration.md) - URL and OAuth configuration
- [`tasks/`](tasks/) - Task management files

## 🤝 Contributing

1. Check current tasks with `task-master list`
2. Pick up the next task with `task-master next`
3. Update task status as you work
4. Follow the existing code patterns and conventions
5. Test thoroughly before marking tasks as complete

## 📄 License

[Add your license information here]

## 🆘 Support

For issues and questions:
1. Check the documentation in `docs/`
2. Review current tasks in `tasks/`
3. Check the GitHub issues
4. Contact the development team

---

**Current Priority**: Complete authentication refactor (Tasks 38-45) to resolve build issues and enable production deployment.
