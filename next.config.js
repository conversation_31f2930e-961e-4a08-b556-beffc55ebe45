/** @type {import('next').NextConfig} */
const nextConfig = {
  // These have moved out of experimental in Next.js 14
  skipTrailingSlashRedirect: true,
  skipMiddlewareUrlNormalize: true,

  experimental: {
    // External packages for server components (removed node-schedule to fix native module error)
    serverComponentsExternalPackages: [],
    // Turbo is still experimental in Next.js 14
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
  webpack: (config, { isServer }) => {
    // Handle common Node.js modules that need externalization
    config.externals.push({
      'utf-8-validate': 'commonjs utf-8-validate',
      'bufferutil': 'commonjs bufferutil',
    });

    // Handle Node.js modules for server-side only
    if (isServer) {
      config.externals.push({
        'node-schedule': 'commonjs node-schedule',
      });
    } else {
      // For client-side, ignore server-only modules
      config.resolve.fallback = {
        ...config.resolve.fallback,
        crypto: false,
        fs: false,
        path: false,
        os: false,
        'node-schedule': false,
      };
    }

    return config;
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: false, // Re-enable TypeScript checking
  },
  output: process.env.STANDALONE === 'true' ? 'standalone' : undefined,
  distDir: '.next',
  // Generate stable build ID
  generateBuildId: async () => {
    return 'build-' + Date.now();
  },
  // Configure static page generation
  trailingSlash: false,
};

module.exports = nextConfig;