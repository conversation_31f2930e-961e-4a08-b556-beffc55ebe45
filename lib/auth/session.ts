import type { SessionOptions } from "iron-session";

export const sessionOptions: SessionOptions = {
  password: process.env.SECRET_COOKIE_PASSWORD as string,
  cookieName: "xsche-session",
  cookieOptions: {
    secure: process.env.NODE_ENV === "production",
  },
};

export interface SessionData {
  codeVerifier?: string;
  state?: string;
  accessToken?: string;
  refreshToken?: string;
}

// This is where we specify the typings of req.session.*
declare module "iron-session" {
  interface IronSessionData extends SessionData {}
} 