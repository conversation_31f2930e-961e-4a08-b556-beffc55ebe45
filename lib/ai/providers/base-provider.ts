/**
 * Base AI Provider interface and error classes
 */

/**
 * Interface for AI providers
 */
export interface AIProvider {
    validateConfig?(): Promise<void>;
    generateText(prompt: string, modelName?: string): Promise<string>;
    generateEmbedding(text: string, modelName?: string): Promise<number[]>;
    getAvailableModels(): Promise<{ chat: string[], embedding: string[] }>;
}

/**
 * Error class for AI provider errors
 */
export class AIProviderError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'AIProviderError';
    }
} 