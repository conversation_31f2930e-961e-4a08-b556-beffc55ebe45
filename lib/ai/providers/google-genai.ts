import { GoogleGenAI } from "@google/genai";
import { AIProvider as AIProviderEnum } from "@prisma/client";
import { AIProviderConfig, GoogleAIConfig } from "../../types/ai";
import { AIConfigurationError } from "../../types/ai";
import { AIProvider, AIProviderError } from "./base-provider";

/**
 * Google GenAI provider
 */
export class GoogleGenAIProvider implements AIProvider {
  private client: GoogleGenAI;
  private apiKey: string;
  private config: GoogleAIConfig;
  private static instances: Map<string, GoogleGenAIProvider> = new Map();

  /**
   * Create a new Google GenAI provider
   * @param config The configuration for the provider
   */
  constructor(config: GoogleAIConfig) {
    if (!config.apiKey) {
      throw new Error("Google API key is required");
    }
    this.apiKey = config.apiKey;
    this.client = new GoogleGenAI({ apiKey: this.apiKey });
    this.config = config;
  }

  /**
   * Get an instance of the GoogleGenAIProvider
   * @param config Configuration for the provider
   * @returns An instance of GoogleGenAIProvider
   */
  public static getInstance(config: GoogleAIConfig): GoogleGenAIProvider {
    const key = config.apiKey;
    if (!GoogleGenAIProvider.instances.has(key)) {
      GoogleGenAIProvider.instances.set(key, new GoogleGenAIProvider(config));
    }
    return GoogleGenAIProvider.instances.get(key)!;
  }

  /**
   * Validate the configuration
   * @returns A promise that resolves if the configuration is valid
   * @throws AIConfigurationError if the configuration is invalid
   */
  async validateConfig(): Promise<void> {
    try {
      // A simple way to validate is to list models.
      // If this fails, the key is likely invalid.
      const modelList = await this.client.models.list();
      for await (const _ of modelList) {
        // Just need to access at least one model to confirm the API works
        break;
      }
    } catch (error) {
      throw new AIConfigurationError("Invalid Google API key or configuration.");
    }
  }

  /**
   * Generate text from a prompt
   * @param prompt The prompt to generate text from
   * @param modelName The model to use (default: from config)
   * @returns A promise that resolves to the generated text
   */
  async generateText(
    prompt: string,
    modelName?: string
  ): Promise<string> {
    const modelToUse = modelName || this.config.defaultChatModel;
    if (!modelToUse) {
      throw new Error("No chat model specified");
    }
    try {
      // Use the generateContent method from the models module
      const result = await this.client.models.generateContent({
        model: modelToUse,
        contents: prompt
      });

      // Shortcut if .text field is provided
      if (result && result.text !== undefined) {
        return result.text;
      }

      const candidate = result?.candidates?.[0];
      if (candidate && candidate.finishReason !== 'STOP' && candidate.content?.parts) {
        return candidate.content.parts
          .map(part => typeof part.text === 'string' ? part.text : '')
          .join('');
      }

      console.error('Unexpected response structure:', JSON.stringify(result));
      throw new Error(`Failed to extract text from Google GenAI response`);
    } catch (error) {
      console.error("Google GenAI generation error:", error);
      throw new AIProviderError(`Failed to generate text with ${modelToUse}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate an embedding for a piece of text
   * @param text The text to generate an embedding for
   * @param modelName The model to use (default: from config)
   * @returns A promise that resolves to the embedding
   */
  async generateEmbedding(
    text: string,
    modelName?: string
  ): Promise<number[]> {
    const modelToUse = modelName || this.config.defaultEmbeddingModel;
    if (!modelToUse) {
      throw new Error("No embedding model specified");
    }
    try {
      // Use the embedContent method for embeddings
      const result = await this.client.models.embedContent({
        model: modelToUse,
        contents: text
      });

      // Check for embeddings in the response
      if (result && result.embeddings && result.embeddings.length > 0 && result.embeddings[0].values) {
        return result.embeddings[0].values;
      } else {
        console.error('Missing embedding values in response:', JSON.stringify(result));
        throw new Error('Failed to extract embedding values from response');
      }
    } catch (error) {
      console.error("Error generating embedding:", error);
      throw new AIProviderError(`Failed to generate embedding with ${modelToUse}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get available models
   * @returns A promise that resolves to an object containing chat and embedding models
   */
  async getAvailableModels(): Promise<{ chat: string[], embedding: string[] }> {
    const chatModels: string[] = [];
    const embeddingModels: string[] = [];

    try {
      const modelList = await this.client.models.list();

      for await (const model of modelList) {
        if (!model.name) continue;

        const modelName = model.name.split('/').pop() || model.name;

        // Classify models: if not for embedding, treat as a chat model.
        if (modelName.includes('embedding')) {
          embeddingModels.push(modelName);
        } else {
          chatModels.push(modelName);
        }
      }

      // If no models were fetched, throw an error
      if (chatModels.length === 0 && embeddingModels.length === 0) {
        throw new AIProviderError("No models available for your API key");
      }

      return {
        chat: chatModels,
        embedding: embeddingModels
      };
    } catch (error) {
      console.error("Failed to fetch Google AI models:", error);
      throw new AIProviderError(`Failed to fetch available models: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Match the config type to this provider
   * @param config The config to check
   * @returns True if the config is for this provider
   */
  public static matchesConfig(config: AIProviderConfig): boolean {
    return config.provider === AIProviderEnum.GOOGLE;
  }
}
