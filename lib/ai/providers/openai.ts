import OpenAI from "openai";
import { APIError } from "openai/error";
import { AIConfigurationError, AIProviderError, OpenAIConfig } from "../../types/ai";

// Define the interface for AI providers
interface AIProviderInterface {
  validateConfig(): Promise<void>;
  getAvailableModels(): Promise<{ chat: string[], embedding: string[] }>;
  generateText(prompt: string, model?: string): Promise<string>;
  generateEmbedding(text: string, model?: string): Promise<number[]>;
}

export class OpenAIProvider implements AIProviderInterface {
  private client: OpenAI;
  private config: OpenAIConfig;
  private static instances: Map<string, OpenAIProvider> = new Map();

  constructor(config: OpenAIConfig) {
    this.config = config;
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
      organization: config.organization,
    });
  }

  public static getInstance(config: OpenAIConfig): OpenAIProvider {
    const key = config.apiKey || process.env.OPENAI_API_KEY || 'default';
    if (!OpenAIProvider.instances.has(key)) {
      OpenAIProvider.instances.set(key, new OpenAIProvider(config));
    }
    return OpenAIProvider.instances.get(key)!;
  }

  async validateConfig(): Promise<void> {
    try {
      await this.client.models.list();
    } catch (error) {
      if (error instanceof APIError) {
        throw new AIConfigurationError(`Failed to validate OpenAI configuration: ${error.message}`);
      }
      throw new AIConfigurationError("Failed to validate OpenAI configuration");
    }
  }

  async getAvailableModels(): Promise<{ chat: string[], embedding: string[] }> {
    try {
      const response = await this.client.models.list();
      const models = response.data;

      const chatModels = models
        .filter(model => model.id.includes('gpt') || model.id.includes('claude') || model.id.includes('o1') || model.id.includes('text-chat') || model.id.includes('code-chat'))
        .map(model => model.id)
        .sort();

      const embeddingModels = models
        .filter(model => model.id.includes('embedding'))
        .map(model => model.id)
        .sort();

      if (chatModels.length === 0 && embeddingModels.length === 0) {
        throw new AIProviderError("No models available for your API key");
      }

      return {
        chat: chatModels,
        embedding: embeddingModels
      };
    } catch (error) {
      console.error("Error fetching OpenAI models:", error);

      // Provide informative error message based on the error type
      if (error instanceof APIError) {
        if (error.status === 401) {
          throw new AIProviderError("Invalid API key or unauthorized access");
        } else if (error.status === 429) {
          throw new AIProviderError("Rate limit exceeded. Please try again later");
        } else if (error.status >= 500) {
          throw new AIProviderError("OpenAI service is currently unavailable. Please try again later");
        }
        throw new AIProviderError(`Failed to fetch OpenAI models: ${error.message}`);
      }

      // If it's our custom error, rethrow it
      if (error instanceof AIProviderError) {
        throw error;
      }

      // Generic error fallback
      throw new AIProviderError("Failed to fetch OpenAI models. Please check your API key and connection");
    }
  }

  async generateText(
    prompt: string,
    model?: string
  ): Promise<string> {
    const messages = [{ role: "user" as const, content: prompt }];

    const operation = async () => {
      const completion = await this.client.chat.completions.create({
        messages,
        model: model || this.config.defaultChatModel,
        max_tokens: 1000,
      });

      return completion.choices[0].message.content || "";
    };

    return this.withRetry(operation);
  }

  async generateEmbedding(text: string, model?: string) {
    const operation = async () => {
      const response = await this.client.embeddings.create({
        input: text,
        model: model || this.config.defaultEmbeddingModel,
      });

      return response.data[0].embedding;
    };

    return this.withRetry(operation);
  }

  /**
   * Streaming chat completion (generator)
   */
  async *generateStreamResponse(
    messages: { role: "user" | "assistant" | "system"; content: string }[],
    model?: string,
    temperature: number = 0.7
  ): AsyncGenerator<{ content: string; done: boolean }, void, unknown> {
    const stream = await this.client.chat.completions.create({
      model: model || this.config.defaultChatModel,
      messages,
      temperature,
      stream: true,
    });

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || "";
      const done = chunk.choices[0]?.finish_reason !== null;
      yield { content, done };
      if (done) break;
    }
  }

  isModelAvailable(modelName: string, modelType: 'chat' | 'embedding'): Promise<boolean> {
    throw new Error("Method not implemented.");
  }

  // Rate limiting utilities
  private async sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (error instanceof Error) {
          lastError = error;

          if (error instanceof APIError && error.status === 429) {
            // Respect Retry-After header if present
            const retryAfter = error.headers?.get("retry-after");
            const retryMs = retryAfter ? parseFloat(retryAfter) * 1000 : Math.pow(2, attempt) * 1000;
            await this.sleep(retryMs);
            continue;
          }
        }

        throw error;
      }
    }

    if (lastError) {
      throw lastError;
    }

    throw new AIProviderError("Max retries exceeded");
  }
}
