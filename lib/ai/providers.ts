/**
 * Enhanced AI Providers with Latest SDK Patterns
 * Using official OpenAI Node.js SDK and Google GenAI SDK
 */

import OpenAI from 'openai';
import { GoogleGenAI } from '@google/genai';
import type { ChatCompletionMessageParam } from 'openai/resources/chat/completions';

// Types for our enhanced AI system
export interface AIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason?: string;
}

export interface StreamChunk {
  content: string;
  done: boolean;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface AIProviderConfig {
  apiKey: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

// Enhanced OpenAI Provider
export class OpenAIProvider {
  private client: OpenAI;
  private config: AIProviderConfig;

  constructor(config: AIProviderConfig) {
    this.config = config;
    this.client = new OpenAI({
      apiKey: config.apiKey,
    });
  }

  async generateResponse(
    messages: AIMessage[],
    systemPrompt?: string
  ): Promise<AIResponse> {
    try {
      const openaiMessages: ChatCompletionMessageParam[] = [];

      if (systemPrompt) {
        openaiMessages.push({
          role: 'system',
          content: systemPrompt,
        });
      }

      openaiMessages.push(
        ...messages.map((msg): ChatCompletionMessageParam => ({
          role: msg.role as 'user' | 'assistant' | 'system',
          content: msg.content,
        }))
      );

      const completion = await this.client.chat.completions.create({
        model: this.config.model,
        messages: openaiMessages,
        temperature: this.config.temperature ?? 0.7,
        max_tokens: this.config.maxTokens ?? 2000,
        top_p: this.config.topP ?? 1,
        frequency_penalty: this.config.frequencyPenalty ?? 0,
        presence_penalty: this.config.presencePenalty ?? 0,
      });

      const choice = completion.choices[0];
      if (!choice?.message?.content) {
        throw new Error('No response content received from OpenAI');
      }

      return {
        content: choice.message.content,
        usage: completion.usage ? {
          promptTokens: completion.usage.prompt_tokens,
          completionTokens: completion.usage.completion_tokens,
          totalTokens: completion.usage.total_tokens,
        } : undefined,
        model: completion.model,
        finishReason: choice.finish_reason || undefined,
      };
    } catch (error) {
      console.error('OpenAI API Error:', error);
      throw new Error(
        error instanceof Error
          ? `OpenAI Error: ${error.message}`
          : 'Unknown OpenAI error'
      );
    }
  }

  async *generateStreamResponse(
    messages: AIMessage[],
    systemPrompt?: string
  ): AsyncGenerator<StreamChunk, void, unknown> {
    try {
      const openaiMessages: ChatCompletionMessageParam[] = [];

      if (systemPrompt) {
        openaiMessages.push({
          role: 'system',
          content: systemPrompt,
        });
      }

      openaiMessages.push(
        ...messages.map((msg): ChatCompletionMessageParam => ({
          role: msg.role as 'user' | 'assistant' | 'system',
          content: msg.content,
        }))
      );

      const stream = await this.client.chat.completions.create({
        model: this.config.model,
        messages: openaiMessages,
        temperature: this.config.temperature ?? 0.7,
        max_tokens: this.config.maxTokens ?? 2000,
        top_p: this.config.topP ?? 1,
        frequency_penalty: this.config.frequencyPenalty ?? 0,
        presence_penalty: this.config.presencePenalty ?? 0,
        stream: true,
      });

      for await (const chunk of stream) {
        const choice = chunk.choices[0];
        const content = choice?.delta?.content || '';
        const done = choice?.finish_reason !== null;

        yield {
          content,
          done,
          usage: chunk.usage ? {
            promptTokens: chunk.usage.prompt_tokens,
            completionTokens: chunk.usage.completion_tokens,
            totalTokens: chunk.usage.total_tokens,
          } : undefined,
        };

        if (done) break;
      }
    } catch (error) {
      console.error('OpenAI Streaming Error:', error);
      throw new Error(
        error instanceof Error
          ? `OpenAI Streaming Error: ${error.message}`
          : 'Unknown OpenAI streaming error'
      );
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      const models = await this.client.models.list();
      const filteredModels = models.data
        .filter(model => {
          // Filter for chat models (excludes embeddings, DALL-E, whisper, etc.)
          return model.id.includes('gpt') ||
            model.id.includes('claude') ||
            model.id.includes('o1');
        })
        .map(model => model.id)
        .sort();

      if (filteredModels.length === 0) {
        throw new Error('No chat models available for your OpenAI API key');
      }

      return filteredModels;
    } catch (error) {
      console.error('Error fetching OpenAI models:', error);
      throw new Error(`Failed to fetch OpenAI models: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Enhanced Google GenAI Provider
export class GoogleGenAIProvider {
  private client: GoogleGenAI;
  private config: AIProviderConfig;

  constructor(config: AIProviderConfig) {
    this.config = config;
    this.client = new GoogleGenAI({
      apiKey: config.apiKey,
    });
  }

  async generateResponse(
    messages: AIMessage[],
    systemPrompt?: string
  ): Promise<AIResponse> {
    try {
      // Convert messages to Google GenAI format
      const contents = messages.map(msg => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }],
      }));

      const generationConfig = {
        temperature: this.config.temperature ?? 0.7,
        maxOutputTokens: this.config.maxTokens ?? 2000,
        topP: this.config.topP ?? 1,
      };

      const response = await this.client.models.generateContent({
        model: this.config.model,
        contents,
        config: {
          ...generationConfig,
          systemInstruction: systemPrompt ? {
            role: 'system',
            parts: [{ text: systemPrompt }],
          } : undefined,
        },
      });

      if (!response.text) {
        throw new Error('No response content received from Google GenAI');
      }

      return {
        content: response.text,
        model: this.config.model,
        usage: response.usageMetadata ? {
          promptTokens: response.usageMetadata.promptTokenCount || 0,
          completionTokens: response.usageMetadata.candidatesTokenCount || 0,
          totalTokens: response.usageMetadata.totalTokenCount || 0,
        } : undefined,
      };
    } catch (error) {
      console.error('Google GenAI Error:', error);
      throw new Error(
        error instanceof Error
          ? `Google GenAI Error: ${error.message}`
          : 'Unknown Google GenAI error'
      );
    }
  }

  async *generateStreamResponse(
    messages: AIMessage[],
    systemPrompt?: string
  ): AsyncGenerator<StreamChunk, void, unknown> {
    try {
      // Convert messages to Google GenAI format
      const contents = messages.map(msg => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }],
      }));

      const generationConfig = {
        temperature: this.config.temperature ?? 0.7,
        maxOutputTokens: this.config.maxTokens ?? 2000,
        topP: this.config.topP ?? 1,
      };

      const stream = await this.client.models.generateContentStream({
        model: this.config.model,
        contents,
        config: {
          ...generationConfig,
          systemInstruction: systemPrompt ? {
            role: 'system',
            parts: [{ text: systemPrompt }],
          } : undefined,
        },
      });

      for await (const chunk of stream) {
        const content = chunk.text || '';
        const done = chunk.candidates?.[0]?.finishReason !== undefined;

        yield {
          content,
          done,
          usage: chunk.usageMetadata ? {
            promptTokens: chunk.usageMetadata.promptTokenCount || 0,
            completionTokens: chunk.usageMetadata.candidatesTokenCount || 0,
            totalTokens: chunk.usageMetadata.totalTokenCount || 0,
          } : undefined,
        };

        if (done) break;
      }
    } catch (error) {
      console.error('Google GenAI Streaming Error:', error);
      throw new Error(
        error instanceof Error
          ? `Google GenAI Streaming Error: ${error.message}`
          : 'Unknown Google GenAI streaming error'
      );
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      const modelList = await this.client.models.list();
      const models: string[] = [];

      for await (const model of modelList) {
        // Skip models without a name
        if (!model.name) continue;

        // Get the model name without the full path
        // Format is usually "models/gemini-1.5-pro" or similar
        const modelName = model.name.split('/').pop();
        if (modelName) {
          models.push(modelName);
        }
      }

      // If no models were found, throw an error
      if (models.length === 0) {
        throw new Error("No Google AI models available for your API key");
      }

      // Sort models alphabetically
      return models.sort();

    } catch (error) {
      console.error('Error fetching Google GenAI models:', error);
      throw new Error(`Failed to fetch Google AI models: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Enhanced AI Provider Factory
export class AIProviderFactory {
  static createProvider(
    provider: 'OPENAI' | 'GOOGLE',
    config: AIProviderConfig
  ): OpenAIProvider | GoogleGenAIProvider {
    switch (provider) {
      case 'OPENAI':
        return new OpenAIProvider(config);
      case 'GOOGLE':
        return new GoogleGenAIProvider(config);
      default:
        throw new Error('Invalid AI provider specified');
    }
  }

  static async getAvailableModels(
    provider: 'OPENAI' | 'GOOGLE',
    apiKey: string
  ): Promise<string[]> {
    let tempProvider;
    const defaultConfig = { apiKey, model: '' }; // Model is required but not for this action

    switch (provider) {
      case 'OPENAI':
        tempProvider = new OpenAIProvider(defaultConfig);
        break;
      case 'GOOGLE':
        tempProvider = new GoogleGenAIProvider(defaultConfig);
        break;
      default:
        throw new Error('Invalid AI provider specified');
    }
    return tempProvider.getAvailableModels();
  }
}

// Enhanced Chat Session Manager
export class ChatSession {
  private provider: OpenAIProvider | GoogleGenAIProvider;
  private messages: AIMessage[] = [];
  private systemPrompt?: string;

  constructor(
    provider: OpenAIProvider | GoogleGenAIProvider,
    systemPrompt?: string
  ) {
    this.provider = provider;
    this.systemPrompt = systemPrompt;
  }

  addMessage(role: 'user' | 'assistant', content: string): void {
    this.messages.push({
      role,
      content,
      timestamp: new Date().toISOString(),
    });
  }

  async sendMessage(content: string): Promise<AIResponse> {
    this.addMessage('user', content);

    const response = await this.provider.generateResponse(
      this.messages,
      this.systemPrompt
    );

    this.addMessage('assistant', response.content);
    return response;
  }

  async *sendMessageStream(content: string): AsyncGenerator<StreamChunk, void, unknown> {
    this.addMessage('user', content);

    let assistantResponse = '';

    for await (const chunk of this.provider.generateStreamResponse(
      this.messages,
      this.systemPrompt
    )) {
      assistantResponse += chunk.content;
      yield chunk;
    }

    this.addMessage('assistant', assistantResponse);
  }

  getMessages(): AIMessage[] {
    return [...this.messages];
  }

  clearMessages(): void {
    this.messages = [];
  }

  setSystemPrompt(prompt: string): void {
    this.systemPrompt = prompt;
  }
}
