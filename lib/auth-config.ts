import { AuthOptions } from "next-auth";
import { JWT } from "next-auth/jwt";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import <PERSON><PERSON>rovider from "next-auth/providers/twitter";
import { Adapter } from "next-auth/adapters";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { PrismaClient } from "@prisma/client";

import { prisma } from "@/lib/prisma";
import * as bcrypt from "bcryptjs";
import { encrypt } from "@/lib/utils/encryption";

export const authOptions: AuthOptions = {
  adapter: PrismaAdapter(prisma) as Adapter,
  providers: [
    CredentialsProvider({
      // The name to display on the sign in form (e.g. "Sign in with...")
      name: "Credentials",
      // `credentials` is used to generate a form on the sign in page.
      // You can specify which fields should be submitted, by adding keys to the `credentials` object.
      // e.g. username, password, email, etc.
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required.");
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email }
        });

        if (!user || !user.hashedPassword) {
          throw new Error("No user found with this email.");
        }

        const isValidPassword = await bcrypt.compare(credentials.password, user.hashedPassword);

        if (!isValidPassword) {
          throw new Error("Incorrect password.");
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
        };
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID ?? "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET ?? "",
    }),
    TwitterProvider({
      clientId: process.env.TWITTER_CLIENT_ID ?? "",
      clientSecret: process.env.TWITTER_CLIENT_SECRET ?? "",
      version: "2.0", // Request OAuth 2.0 flow
    }),
    // TwitterProvider will be added later
  ],
  session: {
    strategy: "jwt", // Use JWT for stateless sessions
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === "google" || account?.provider === "twitter") {
        // Extract profile information based on provider
        let profileName = user.name;
        let profileImage = user.image;
        let twitterScreenName = null;

        if (account.provider === "twitter" && profile) {
          profileName = (profile as any).data?.name || user.name;
          profileImage = (profile as any).data?.profile_image_url || user.image;
          twitterScreenName = (profile as any).data?.username;
        }

        // For any OAuth provider, upsert the LinkedAccount
        await (prisma as PrismaClient).linkedAccount.upsert({
          where: {
            provider_providerAccountId: {
              provider: account.provider,
              providerAccountId: account.providerAccountId,
            }
          },
          create: {
            userId: user.id,
            provider: account.provider,
            providerAccountId: account.providerAccountId,
            accessToken: account.access_token
              ? encrypt(account.access_token)
              : null,
            refreshToken: account.refresh_token
              ? encrypt(account.refresh_token)
              : null,
            expiresAt: account.expires_at,
            // Add profile info for display
            name: profileName,
            profilePicture: profileImage,
            screenName: twitterScreenName,
          },
          update: {
            accessToken: account.access_token
              ? encrypt(account.access_token)
              : null,
            refreshToken: account.refresh_token
              ? encrypt(account.refresh_token)
              : null,
            expiresAt: account.expires_at,
            // Update profile info
            name: profileName,
            profilePicture: profileImage,
            screenName: twitterScreenName,
          },
        });
      }
      return true;
    },
    async jwt({ token, user, account }) {
      // Persist the OAuth access_token to the token right after signin
      if (account) {
        token.accessToken = account.access_token;
      }
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      // Send properties to the client, like an access_token from a provider.
      if (token) {
        session.user.id = token.id as string;
        session.accessToken = token.accessToken as string;
      }
      return session;
    },
  },
  // Add pages configuration later if needed (signIn, signOut, error, verifyRequest, newUser)
  // pages: {
  //   signIn: '/login', // Specify custom login page
  // },
  // Add secret here (from environment variable)
  secret: process.env.NEXTAUTH_SECRET,
  // Add debug logging in development
  debug: process.env.NODE_ENV === "development",
};
