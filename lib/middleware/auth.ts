import { NextRequest, NextResponse } from 'next/server';
import { SignJWT, jwtVerify } from 'jose';
import { config } from '../config';
import { UserJwtPayload } from '../types/auth';

const secretKey = new TextEncoder().encode(config.auth.jwt.secret);
const alg = 'HS256';

async function signJWT(payload: UserJwtPayload, expiresIn: string = '7d'): Promise<string> {
  return new SignJWT(payload)
    .setProtectedHeader({ alg })
    .setIssuedAt()
    .setExpirationTime(expiresIn)
    .sign(secretKey);
}

export async function verifyJWT(token: string): Promise<UserJwtPayload | null> {
  try {
    const { payload } = await jwtVerify(token, secretKey, {
      algorithms: [alg],
    });
    return payload as UserJwtPayload;
  } catch (error) {
    // Token is invalid or expired
    return null;
  }
}

export async function validateAuth(request: NextRequest) {
  try {
    const token = request.cookies.get(config.auth.session.cookieName)?.value;

    if (!token) {
      throw new Error('No token found');
    }

    const decoded = await verifyJWT(token);
    return decoded;
  } catch (error) {
    return null;
  }
}

export async function requireAuth(request: NextRequest) {
  const user = await validateAuth(request);

  if (!user) {
    return NextResponse.json(
      { success: false, error: 'Unauthorized' },
      { status: 401 }
    );
  }

  return user;
}

export function setAuthCookie(response: NextResponse, token: string) {
  response.cookies.set({
    name: config.auth.session.cookieName,
    value: token,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: config.auth.session.maxAge,
    path: '/',
  });
}

export function clearAuthCookie(response: NextResponse) {
  response.cookies.delete(config.auth.session.cookieName);
}

export async function generateAuthToken(user: UserJwtPayload): Promise<string> {
  const expiresIn = config.auth.jwt.expiresIn || '7d';
  return signJWT(user, expiresIn);
}