/**
 * API configuration settings
 */
export const apiConfig = {
  /**
   * Timeout settings for API requests
   */
  timeouts: {
    /**
     * Default timeout for API requests in milliseconds
     */
    default: 5000,
    
    /**
     * Model validation timeout in milliseconds
     * This is used when testing multiple models in parallel
     */
    modelValidation: {
      /**
       * Base timeout value in milliseconds
       */
      base: 3000,
      
      /**
       * Additional time per model in milliseconds
       * Total timeout = base + (perModel * numberOfModels)
       */
      perModel: 500,
      
      /**
       * Maximum timeout value in milliseconds
       */
      max: 15000
    }
  }
};

