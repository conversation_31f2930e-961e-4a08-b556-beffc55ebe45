import { EnhancedPersonaTemplate } from "./types";

export class EnhancedPersonalityParser {
  /**
   * Generate a system prompt from an enhanced persona template
   * @param persona Enhanced persona template
   * @returns System prompt string
   */
  static generateSystemPrompt(persona: EnhancedPersonaTemplate): string {
    // If the persona has a system field, use it directly
    if (persona.system && persona.system.trim()) {
      return persona.system.trim();
    }
    
    // Otherwise, generate a system prompt from the persona fields
    const parts: string[] = [`You are a Twitter bot with the following personality:`];
    
    // Add basic information
    parts.push(`Name: ${persona.name}`);
    
    // Add bio if available
    if (persona.bio && persona.bio.length > 0) {
      parts.push(`Bio: ${persona.bio.join("\n")}`);
    }
    
    // Add lore if available
    if (persona.lore && persona.lore.length > 0) {
      parts.push(`Background: ${persona.lore.join("\n")}`);
    }
    
    // Add adjectives if available
    if (persona.adjectives && persona.adjectives.length > 0) {
      parts.push(`Personality Traits: ${persona.adjectives.join(", ")}`);
    }
    
    // Add topics if available
    if (persona.topics && persona.topics.length > 0) {
      parts.push(`Knowledge Areas:\n${persona.topics.map(t => `- ${t}`).join("\n")}`);
    }
    
    // Add style guidelines if available
    if (persona.style) {
      if (persona.style.all && persona.style.all.length > 0) {
        parts.push(`Style Guidelines:\n${persona.style.all.map(s => `- ${s}`).join("\n")}`);
      }
      
      if (persona.style.chat && persona.style.chat.length > 0) {
        parts.push(`Chat Style:\n${persona.style.chat.map(s => `- ${s}`).join("\n")}`);
      }
      
      if (persona.style.post && persona.style.post.length > 0) {
        parts.push(`Post Style:\n${persona.style.post.map(s => `- ${s}`).join("\n")}`);
      }
    }
    
    // Add message examples if available
    if (persona.messageExamples && persona.messageExamples.length > 0) {
      parts.push(`Example Conversations: ${JSON.stringify(persona.messageExamples)}`);
    }
    
    // Add post examples if available
    if (persona.postExamples && persona.postExamples.length > 0) {
      parts.push(`Example Posts:\n${persona.postExamples.map(p => `- ${p}`).join("\n")}`);
    }
    
    // Add Twitter-specific instructions
    parts.push(`Generate tweets that reflect this personality while staying within Twitter's character limits.`);
    
    return parts.join("\n\n");
  }
  
  /**
   * Generate a context-specific prompt enhancement based on persona fields
   * @param persona Enhanced persona template
   * @param context The context type (e.g., 'tweet', 'chat')
   * @returns Context-specific prompt enhancement
   */
  static generateContextPrompt(persona: EnhancedPersonaTemplate, context: 'tweet' | 'chat'): string {
    const parts: string[] = [];
    
    // Add style guidelines based on context
    if (persona.style) {
      // Add general style guidelines
      if (persona.style.all && persona.style.all.length > 0) {
        parts.push(`Follow these style guidelines:\n${persona.style.all.map(s => `- ${s}`).join("\n")}`);
      }
      
      // Add context-specific style guidelines
      if (context === 'chat' && persona.style.chat && persona.style.chat.length > 0) {
        parts.push(`For chat responses, follow these additional guidelines:\n${persona.style.chat.map(s => `- ${s}`).join("\n")}`);
      } else if (context === 'tweet' && persona.style.post && persona.style.post.length > 0) {
        parts.push(`For tweets, follow these additional guidelines:\n${persona.style.post.map(s => `- ${s}`).join("\n")}`);
      }
    }
    
    // Add topics to focus on
    if (persona.topics && persona.topics.length > 0) {
      parts.push(`Focus on these topics when relevant:\n${persona.topics.map(t => `- ${t}`).join("\n")}`);
    }
    
    // Add personality traits/adjectives
    if (persona.adjectives && persona.adjectives.length > 0) {
      parts.push(`Embody these personality traits: ${persona.adjectives.join(", ")}`);
    }
    
    // Add examples based on context
    if (context === 'chat' && persona.messageExamples && persona.messageExamples.length > 0) {
      parts.push(`Reference these conversation examples for tone and style: ${JSON.stringify(persona.messageExamples)}`);
    } else if (context === 'tweet' && persona.postExamples && persona.postExamples.length > 0) {
      parts.push(`Reference these example posts for tone and style:\n${persona.postExamples.map(p => `- ${p}`).join("\n")}`);
    }
    
    return parts.join("\n\n");
  }
}

