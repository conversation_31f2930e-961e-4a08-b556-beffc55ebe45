import { BotPersona } from "@prisma/client";

/**
 * Basic persona template (legacy format)
 */
export interface PersonaTemplate {
  name?: string;
  description?: string;
  tone?: string;
  writingStyle?: string;
  vocabulary?: string;
  traits?: string[];
  knowledge?: string[];
  rules?: string[];
  contextLength?: number;
  dialogues?: { user: string; bot: string; }[];
}

/**
 * Enhanced persona template with additional fields
 */
export interface EnhancedPersonaTemplate {
  name?: string;
  system?: string;
  bio?: string[];
  lore?: string[];
  messageExamples?: any[];
  postExamples?: string[];
  adjectives?: string[];
  topics?: string[];
  style?: {
    all?: string[];
    chat?: string[];
    post?: string[];
  };
  version?: number;
}

/**
 * Bot configuration
 */
export interface BotConfig {
  id: string;
  name?: string;
  description?: string;
  aiProvider: string;
  chatModel?: string | null;
  embeddingModel?: string | null;
  persona: PersonaTemplate | EnhancedPersonaTemplate;
}

/**
 * Context for content generation
 */
export interface GenerationContext {
  userPrompt?: string;
  recentTweets?: string[];
  brainEntries?: string[];
  maxTokens?: number;
}

/**
 * Result of content generation
 */
export interface GenerationResult {
  content: string;
  tokens: number;
}

/**
 * Custom error for persona validation failures
 */
export class PersonaValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "PersonaValidationError";
  }
}

/**
 * Custom error for bot generation failures
 */
export class BotGenerationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "BotGenerationError";
  }
}

/**
 * Result of persona import operation
 */
export interface PersonaImportResult {
  success: boolean;
  error?: string;
  personaId?: string;
}

/**
 * Options for persona export
 */
export interface PersonaExportOptions {
  includeMetadata?: boolean;
}

/**
 * Result of persona export operation
 */
export interface PersonaExportResult {
  success: boolean;
  error?: string;
  data?: string;
}

/**
 * User preferences for personas
 */
export interface PersonaUserPreferences {
  activePersonaId?: string;
}

/**
 * Persona with additional metadata
 */
export interface PersonaWithMetadata extends BotPersona {
  // No need to redefine isActive as it's already in BotPersona
}
