import { z } from "zod";
import { PersonaTemplate, PersonaValidationError } from "./types";

const personaSchema = z.object({
  name: z.string().min(1).max(50),
  description: z.string().max(500).optional(),
  tone: z.string().min(1).max(100),
  writingStyle: z.string().min(1).max(200),
  vocabulary: z.string().min(1).max(200),
  traits: z.array(z.string()).min(1).max(10),
  knowledge: z.array(z.string()).min(1).max(20),
  rules: z.array(z.string()).min(1).max(10),
  contextLength: z.number().int().min(100).max(2000)
});

export class PersonalityParser {
  /**
   * Parse and validate a persona file
   * @param content Raw file content as string
   * @returns Validated PersonaTemplate
   * @throws PersonaValidationError
   */
  static parsePersona(content: string): PersonaTemplate {
    try {
      // Parse JSON content
      const rawPersona = JSON.parse(content);
      
      // Validate against schema
      const result = personaSchema.safeParse(rawPersona);
      
      if (!result.success) {
        throw new Error(result.error.message);
      }

      // Normalize data
      const persona = result.data;
      
      return {
        name: persona.name.trim(),
        description: persona.description?.trim(),
        tone: persona.tone.trim().toLowerCase(),
        writingStyle: persona.writingStyle.trim(),
        vocabulary: persona.vocabulary.trim(),
        traits: persona.traits.map(t => t.trim()),
        knowledge: persona.knowledge.map(k => k.trim()),
        rules: persona.rules.map(r => r.trim()),
        contextLength: persona.contextLength
      };
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new PersonaValidationError("Invalid JSON format in persona file");
      }
      if (error instanceof Error) {
        throw new PersonaValidationError(
          `Persona validation failed: ${error.message}`
        );
      }
      throw new PersonaValidationError("Failed to parse persona file");
    }
  }

  /**
   * Generate a system prompt from a persona template
   */
  static generateSystemPrompt(persona: PersonaTemplate): string {
    const description = persona.description ? `Description: ${persona.description}\n` : "";
    const traits = (persona.traits || []).map(t => `- ${t}`).join("\n");
    const knowledge = (persona.knowledge || []).map(k => `- ${k}`).join("\n");
    const rules = (persona.rules || []).map(r => `- ${r}`).join("\n");
    const dialogues = (persona.dialogues || []).map(d => `User: ${d.user}\nBot: ${d.bot}`).join("\n\n");

    return `You are a Twitter bot with the following personality:

Name: ${persona.name}
${description}Tone: ${persona.tone}
Writing Style: ${persona.writingStyle}
Vocabulary Level: ${persona.vocabulary}

Key Traits:
${traits}

Knowledge Areas:
${knowledge}

Rules to Follow:
${rules}

Example Dialogues:
${dialogues}

Generate tweets that reflect this personality while staying within Twitter's character limits.`;
  }

  // Function to generate a prompt for creating a new persona from a description
  static generateCreationPrompt(description: string): string {
    return `Based on the following description, create a detailed persona in JSON format.

Description:
"${description}"

Generate a JSON object with the following structure:
{
  "name": "A suitable name for the persona",
  "description": "A brief summary of the persona's character",
  "tone": "The persona's typical tone (e.g., 'witty', 'formal', 'friendly')",
  "writingStyle": "Describe the writing style (e.g., 'uses short sentences', 'prefers emojis')",
  "vocabulary": "The persona's vocabulary level (e.g., 'simple', 'academic', 'technical')",
  "traits": ["A list of key personality traits"],
  "knowledge": ["A list of topics the persona is knowledgeable about"],
  "rules": ["A list of rules the persona must follow"],
  "contextLength": 1000
}`;
  }
}