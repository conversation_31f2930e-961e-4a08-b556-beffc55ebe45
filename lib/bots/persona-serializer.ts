import { <PERSON>t<PERSON>erson<PERSON> } from "@prisma/client";
import { EnhancedPersonaTemplate, PersonaExportOptions } from "./types";
import { z } from "zod";
import { JsonValue } from "@prisma/client/runtime/library";

// Create a properly typed persona interface that exactly matches the Prisma schema
// but allows for safe access to the JSON fields
type EnhancedBotPersona = BotPersona;

/**
 * Handles serialization and deserialization of persona data
 */
export class PersonaSerializer {
  /**
   * Validates and converts JSON string to persona data
   * @param jsonData JSON string containing persona data
   * @returns Parsed persona data or null if invalid
   */
  static fromJson(jsonData: string): EnhancedPersonaTemplate | null {
    try {
      // Parse JSON
      const parsed = JSON.parse(jsonData);

      // Define schema for validation
      const personaSchema = z.object({
        name: z.string().min(1, "Name is required"),
        system: z.string().min(1, "System prompt is required"),
        bio: z.array(z.string()).min(1, "At least one bio entry is required"),
        lore: z.array(z.string()).optional(),
        messageExamples: z.array(z.any()).optional(),
        postExamples: z.array(z.string()).optional(),
        adjectives: z.array(z.string()).optional(),
        topics: z.array(z.string()).optional(),
        style: z.object({
          all: z.array(z.string()).optional(),
          chat: z.array(z.string()).optional(),
          post: z.array(z.string()).optional()
        }).optional(),
        version: z.number().optional()
      });

      // Validate against schema
      const result = personaSchema.safeParse(parsed);

      if (!result.success) {
        console.error("Persona validation failed:", result.error);
        return null;
      }

      return result.data;
    } catch (error) {
      console.error("Error parsing persona JSON:", error);
      return null;
    }
  }

  /**
   * Converts persona data to JSON string
   * @param persona Persona data to convert
   * @param options Export options
   * @returns JSON string representation of the persona
   */
  static toJson(persona: EnhancedBotPersona, options: PersonaExportOptions = {}): string {
    try {
      // Create a clean version of the persona without database metadata
      const cleanPersona: Record<string, any> = {
        name: persona.name,
        system: persona.system,
        bio: persona.bio || [],
        version: 1
      };

      // Add optional fields if they exist
      if (persona.lore && Array.isArray(persona.lore) && persona.lore.length > 0) {
        cleanPersona.lore = persona.lore;
      }

      if (persona.messageExamples && Array.isArray(persona.messageExamples) && persona.messageExamples.length > 0) {
        cleanPersona.messageExamples = persona.messageExamples;
      }

      if (persona.postExamples && Array.isArray(persona.postExamples) && persona.postExamples.length > 0) {
        cleanPersona.postExamples = persona.postExamples;
      }

      if (persona.adjectives && Array.isArray(persona.adjectives) && persona.adjectives.length > 0) {
        cleanPersona.adjectives = persona.adjectives;
      }

      if (persona.topics && Array.isArray(persona.topics) && persona.topics.length > 0) {
        cleanPersona.topics = persona.topics;
      }

      if (persona.style && typeof persona.style === 'object') {
        cleanPersona.style = persona.style;
      }

      // Include metadata if requested
      if (options.includeMetadata) {
        cleanPersona.metadata = {
          id: persona.id,
          createdAt: persona.createdAt,
          updatedAt: persona.updatedAt
        };
      }

      return JSON.stringify(cleanPersona, null, 2);
    } catch (error) {
      console.error("Error serializing persona to JSON:", error);
      throw new Error("Failed to serialize persona");
    }
  }

  /**
   * Converts a database persona to an enhanced template
   * @param persona Database persona record
   * @returns Enhanced persona template
   */
  static toTemplate(persona: BotPersona): EnhancedPersonaTemplate {
    // Helper function to convert JsonValue to string array
    const toStringArray = (value: any): string[] => {
      if (!value) return [];
      if (Array.isArray(value)) {
        return value.map(item => String(item)).filter(Boolean);
      }
      if (typeof value === 'string') {
        return [value];
      }
      return [];
    };

    // Helper function to convert JsonValue to an object with string array properties
    const toStyleObject = (value: any): EnhancedPersonaTemplate['style'] => {
      const defaultStyle = { all: [], chat: [], post: [] };
      if (!value || typeof value !== 'object') return defaultStyle;
      
      return {
        all: toStringArray((value as any).all || []),
        chat: toStringArray((value as any).chat || []),
        post: toStringArray((value as any).post || [])
      };
    };

    return {
      name: persona.name,
      system: persona.system || "",
      bio: toStringArray(persona.bio),
      lore: toStringArray(persona.lore),
      messageExamples: Array.isArray(persona.messageExamples) ? persona.messageExamples : [],
      postExamples: toStringArray(persona.postExamples),
      adjectives: toStringArray(persona.adjectives),
      topics: toStringArray(persona.topics),
      style: toStyleObject(persona.style),
      version: persona.version
    };
  }
}

