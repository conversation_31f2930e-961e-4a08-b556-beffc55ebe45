import { Bot<PERSON>erson<PERSON> } from "@prisma/client";
import { PersonaSerializer } from "./persona-serializer";
import {
  EnhancedPersonaTemplate,
  PersonaImportResult,
  PersonaExportOptions,
  PersonaExportResult
} from "./types";

/**
 * Service for managing bot personas
 */
export class PersonaService {
  private db: any; // Use any to accept extended Prisma client

  constructor(db: any) {
    this.db = db;
  }

  /**
   * Get all personas for a user
   * @param userId User ID
   * @returns Array of personas
   */
  async getUserPersonas(userId: string): Promise<BotPersona[]> {
    return this.db.botPersona.findMany({
      where: { userId },
      orderBy: { updatedAt: "desc" }
    });
  }

  /**
   * Get a specific persona by ID
   * @param personaId Persona ID
   * @returns Persona or null if not found
   */
  async getPersona(personaId: string): Promise<BotPersona | null> {
    return this.db.botPersona.findUnique({
      where: { id: personaId }
    });
  }

  /**
   * Create a new persona
   * @param userId User ID
   * @param personaData Persona data
   * @returns Created persona
   */
  async createPersona(
    userId: string,
    personaData: EnhancedPersonaTemplate
  ): Promise<BotPersona> {
    return this.db.botPersona.create({
      data: {
        userId,
        name: personaData.name,
        description: `Enhanced persona: ${personaData.name}`,
        personaContent: personaData, // Store the full template in the legacy field for compatibility
        personaType: "FILE",
        aiProvider: "OPENAI",
        system: personaData.system,
        bio: personaData.bio,
        lore: personaData.lore || [],
        messageExamples: personaData.messageExamples || [],
        postExamples: personaData.postExamples || [],
        adjectives: personaData.adjectives || [],
        topics: personaData.topics || [],
        style: personaData.style || {
          all: [],
          chat: [],
          post: []
        },
        version: personaData.version || 1,
        isActive: false
      }
    });
  }

  /**
   * Update an existing persona
   * @param personaId Persona ID
   * @param personaData Updated persona data
   * @returns Updated persona
   */
  async updatePersona(
    personaId: string,
    personaData: Partial<EnhancedPersonaTemplate>
  ): Promise<BotPersona> {
    return this.db.botPersona.update({
      where: { id: personaId },
      data: {
        ...(personaData.name && { name: personaData.name }),
        ...(personaData.system && { system: personaData.system }),
        ...(personaData.bio && { bio: personaData.bio }),
        ...(personaData.lore && { lore: personaData.lore }),
        ...(personaData.messageExamples && { messageExamples: personaData.messageExamples }),
        ...(personaData.postExamples && { postExamples: personaData.postExamples }),
        ...(personaData.adjectives && { adjectives: personaData.adjectives }),
        ...(personaData.topics && { topics: personaData.topics }),
        ...(personaData.style && { style: personaData.style }),
        ...(personaData.version && { version: personaData.version })
      }
    });
  }

  /**
   * Delete a persona
   * @param personaId Persona ID
   */
  async deletePersona(personaId: string): Promise<void> {
    // Delete the persona (isActive field will be automatically handled)
    await this.db.botPersona.delete({
      where: { id: personaId }
    });
  }

  /**
   * Get the active persona for a user
   * @param userId User ID
   * @returns Active persona or null if none set
   */
  async getActivePersona(userId: string): Promise<BotPersona | null> {
    return this.db.botPersona.findFirst({
      where: {
        userId,
        isActive: true
      }
    });
  }

  /**
   * Set a persona as active for a user
   * @param userId User ID
   * @param personaId Persona ID
   * @returns Active persona
   */
  async setActivePersona(userId: string, personaId: string): Promise<BotPersona> {
    // Get the persona to ensure it exists and belongs to the user
    const persona = await this.db.botPersona.findFirst({
      where: {
        id: personaId,
        userId
      }
    });

    if (!persona) {
      throw new Error("Persona not found");
    }

    // First, set all personas for this user to inactive
    await this.db.botPersona.updateMany({
      where: { userId },
      data: { isActive: false }
    });

    // Then set the selected persona as active
    const updatedPersona = await this.db.botPersona.update({
      where: { id: personaId },
      data: { isActive: true }
    });

    return updatedPersona;
  }

  /**
   * Import a persona from JSON
   * @param userId User ID
   * @param jsonData JSON string containing persona data
   * @returns Import result
   */
  async importPersona(userId: string, jsonData: string): Promise<PersonaImportResult> {
    try {
      // Parse and validate JSON
      const personaData = PersonaSerializer.fromJson(jsonData);

      if (!personaData) {
        return {
          success: false,
          error: "Invalid persona data format"
        };
      }

      // Create the persona
      const persona = await this.createPersona(userId, personaData);

      return {
        success: true,
        personaId: persona.id
      };
    } catch (error) {
      console.error("Error importing persona:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }

  /**
   * Export a persona to JSON
   * @param personaId Persona ID
   * @param options Export options
   * @returns Export result
   */
  async exportPersona(
    personaId: string,
    options: PersonaExportOptions = {}
  ): Promise<PersonaExportResult> {
    try {
      // Get the persona
      const persona = await this.getPersona(personaId);

      if (!persona) {
        return {
          success: false,
          error: "Persona not found"
        };
      }

      // Convert to JSON
      const jsonData = PersonaSerializer.toJson(persona, options);

      return {
        success: true,
        data: jsonData
      };
    } catch (error) {
      console.error("Error exporting persona:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }

  /**
   * Get all personas for a user with filtering, sorting, and pagination
   * @param userId User ID
   * @param options Filtering, sorting, and pagination options
   * @returns Array of personas and total count
   */
  async getUserPersonasWithFilters(
    userId: string,
    options: {
      searchTerm?: string;
      sortField?: 'name' | 'createdAt' | 'updatedAt';
      sortOrder?: 'asc' | 'desc';
      page?: number;
      limit?: number;
    }
  ): Promise<{ personas: BotPersona[]; totalCount: number }> {
    const {
      searchTerm = '',
      sortField = 'updatedAt',
      sortOrder = 'desc',
      page = 1,
      limit = 20
    } = options;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build the where clause for filtering
    const where: any = { userId };

    // Add search term filtering if provided
    if (searchTerm) {
      where.OR = [
        { name: { contains: searchTerm, mode: 'insensitive' } },
        { description: { contains: searchTerm, mode: 'insensitive' } },
        { system: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }

    // Get total count for pagination
    const totalCount = await this.db.botPersona.count({ where });

    // Get personas with filtering, sorting, and pagination
    const personas = await this.db.botPersona.findMany({
      where,
      orderBy: { [sortField]: sortOrder },
      skip: offset,
      take: limit
    });

    return { personas, totalCount };
  }


}
