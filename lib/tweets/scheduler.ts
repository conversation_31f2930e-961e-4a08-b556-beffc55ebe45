import { prisma } from '../prisma';
import { ScheduledTweet, TweetStatus, LinkedAccount } from '@prisma/client';
import { twitterPublisher } from './publisher';
import { TweetPublishOptions } from './types';

const MAX_RETRIES = 3;
const RETRY_DELAYS = [2 * 60 * 1000, 10 * 60 * 1000, 30 * 60 * 1000]; // 2min, 10min, 30min

type ScheduledTweetWithRelations = ScheduledTweet & {
  twitterAccount: LinkedAccount;
};

export class TweetScheduler {
  private schedulerInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.schedulerInterval = null;
  }

  public start(): void {
    if (this.schedulerInterval) {
      return; // Already running
    }

    // Check for tweets to publish every minute
    this.schedulerInterval = setInterval(async () => {
      try {
        await this.processScheduledTweets();
      } catch (error) {
        console.error('Error processing scheduled tweets:', error);
      }
    }, 60000);
  }

  public stop(): void {
    if (this.schedulerInterval) {
      clearInterval(this.schedulerInterval);
      this.schedulerInterval = null;
    }
  }

  private async processScheduledTweets(): Promise<void> {
    const now = new Date();

    // Find tweets that are due for publishing
    const dueTweets = await prisma.scheduledTweet.findMany({
      where: {
        scheduledAt: {
          lte: now
        },
        status: 'PENDING'
      },
      include: {
        twitterAccount: true,
        botPersona: true
      }
    });

    for (const tweet of dueTweets) {
      try {
        await this.publishTweet(tweet as ScheduledTweetWithRelations);
      } catch (error) {
        console.error(`Failed to publish tweet ${tweet.id}:`, error);
        await this.handleFailedTweet(tweet);
      }
    }
  }

  private async publishTweet(tweet: ScheduledTweetWithRelations): Promise<void> {
    const mediaUrls = tweet.mediaUrlsJson ? JSON.parse(tweet.mediaUrlsJson as string) : [];

    // Prepare media array if present
    const media = mediaUrls.length > 0 ? await this.prepareMedia(mediaUrls, tweet.userId) : undefined;

    // Prepare publish options
    const publishOptions: TweetPublishOptions = {
      content: tweet.content,
      twitterAccount: tweet.twitterAccount,
      media
    };

    // Attempt to publish
    const result = await twitterPublisher.publish(publishOptions);

    if (result.success) {
      // Update tweet status to published
      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: {
          status: 'PUBLISHED',
          publishedTweetId: result.tweetId
        }
      });
    } else {
      throw new Error(result.error?.message || 'Unknown error publishing tweet');
    }
  }

  private async handleFailedTweet(tweet: ScheduledTweet): Promise<void> {
    const retryCount = await this.getRetryCount(tweet.id);

    if (retryCount < MAX_RETRIES) {
      // Schedule retry with exponential backoff
      const retryDelay = RETRY_DELAYS[retryCount] || RETRY_DELAYS[RETRY_DELAYS.length - 1];
      const nextRetry = new Date(Date.now() + retryDelay);

      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: {
          scheduledAt: nextRetry,
          status: 'PENDING'
        }
      });

      console.log(`Scheduled retry ${retryCount + 1}/${MAX_RETRIES} for tweet ${tweet.id} at ${nextRetry} (delay: ${retryDelay}ms)`);
    } else {
      // Mark as failed after max retries
      await prisma.scheduledTweet.update({
        where: { id: tweet.id },
        data: {
          status: 'FAILED'
        }
      });

      console.error(`Tweet ${tweet.id} failed after ${MAX_RETRIES} retries`);
    }
  }

  private async getRetryCount(tweetId: string): Promise<number> {
    // Count status changes to determine retry attempts
    const changes = await prisma.scheduledTweet.findUnique({
      where: { id: tweetId },
      select: {
        status: true
      }
    });

    return changes ? 1 : 0; // Basic implementation, could be enhanced with status history tracking
  }

  private async prepareMedia(mediaUrls: string[], userId: string) {
    const mediaFiles = await prisma.uploadedFile.findMany({
      where: {
        userId,
        fileURL: {
          in: mediaUrls
        }
      }
    });

    return mediaFiles.map(file => ({
      file,
      twitterMediaId: undefined
    }));
  }
}

// Export singleton instance
export const tweetScheduler = new TweetScheduler();