import { decrypt } from '../utils/encryption';
import { UploadedFile, TwitterAccount } from '@prisma/client';
import { TweetPublisher, TweetPublishOptions, PublishResult, TweetMedia } from './types';
import { RateLimiter } from 'limiter';

// Lazy import to avoid circular dependency issues
let TwitterApi: any = null;
let ApiResponseError: any = null;
let ApiRequestError: any = null;

async function getTwitterApiClasses() {
  if (!TwitterApi) {
    try {
      const twitterModule = await import('twitter-api-v2');
      TwitterApi = twitterModule.TwitterApi;
      ApiResponseError = twitterModule.ApiResponseError;
      ApiRequestError = twitterModule.ApiRequestError;
    } catch (error) {
      console.error('Failed to import twitter-api-v2:', error);
      throw new Error('Twitter API library not available');
    }
  }
  return { Twitter<PERSON><PERSON>, ApiResponseError, ApiRequestError };
}

// Rate limits for Twitter API v2
const TWEET_RATE_LIMIT = 50; // tweets per 15 minutes window
const MEDIA_UPLOAD_RATE_LIMIT = 25; // uploads per 15 minutes window

export class TwitterPublisher implements TweetPublisher {
  private tweetLimiter: RateLimiter;
  private mediaLimiter: RateLimiter;

  constructor() {
    // Initialize rate limiters (15 minutes = 900000ms)
    this.tweetLimiter = new RateLimiter({
      tokensPerInterval: TWEET_RATE_LIMIT,
      interval: 900000 // 15 minutes in milliseconds
    });

    this.mediaLimiter = new RateLimiter({
      tokensPerInterval: MEDIA_UPLOAD_RATE_LIMIT,
      interval: 900000 // 15 minutes in milliseconds
    });
  }

  private async getTwitterClient(account: TwitterAccount): Promise<any> {
    const { TwitterApi } = await getTwitterApiClasses();
    const accessToken = await decrypt(account.accessToken);

    if (!accessToken) {
      throw new Error('Invalid Twitter credentials');
    }

    // Enhanced Twitter client with OAuth2 Bearer token
    const client = new TwitterApi(accessToken);

    // Verify credentials and return appropriate client
    try {
      await client.v2.me();
      return client;
    } catch (error) {
      throw new Error(`Twitter authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async waitForRateLimit(limiter: RateLimiter): Promise<void> {
    const remainingTokens = await limiter.removeTokens(1);
    if (remainingTokens < 0) {
      throw new Error('Rate limit exceeded');
    }
  }

  private formatMediaIds(ids: string[]): [string] | [string, string] | [string, string, string] | [string, string, string, string] {
    const count = Math.min(ids.length, 4);
    switch (count) {
      case 1:
        return [ids[0]];
      case 2:
        return [ids[0], ids[1]];
      case 3:
        return [ids[0], ids[1], ids[2]];
      case 4:
        return [ids[0], ids[1], ids[2], ids[3]];
      default:
        return [ids[0]]; // Fallback to single media
    }
  }

  public async publish(options: TweetPublishOptions): Promise<PublishResult> {
    try {
      const { ApiResponseError, ApiRequestError } = await getTwitterApiClasses();
      await this.waitForRateLimit(this.tweetLimiter);

      const client = await this.getTwitterClient(options.twitterAccount);

      let mediaIds: string[] = [];
      if (options.media?.length) {
        // Limit to 4 media items as per Twitter's limit
        const mediaToUpload = options.media.slice(0, 4);
        for (const media of mediaToUpload) {
          if (!media.twitterMediaId) {
            const mediaId = await this.uploadMedia(media.file, options.twitterAccount);
            mediaIds.push(mediaId);
          } else {
            mediaIds.push(media.twitterMediaId);
          }
        }
      }

      // Prepare tweet parameters
      const tweetParams: any = {
        text: options.content
      };

      if (mediaIds.length > 0) {
        tweetParams.media = {
          media_ids: this.formatMediaIds(mediaIds)
        };
      }

      // Post tweet
      const tweetData = await client.v2.tweet(tweetParams);

      return {
        success: true,
        tweetId: tweetData.data.id
      };

    } catch (unknownError: unknown) {
      console.error('Tweet publish error:', unknownError);

      // Enhanced error handling for Twitter API specific errors
      const { ApiResponseError, ApiRequestError } = await getTwitterApiClasses();

      // Generic error object that we'll populate based on error type
      let errorMessage = 'Unknown error occurred';
      let errorCode = 'UNKNOWN_ERROR';

      if (unknownError instanceof Error) {
        errorMessage = unknownError.message;
        errorCode = unknownError.name;
      }

      // Twitter API specific errors
      if (ApiResponseError && unknownError instanceof ApiResponseError) {
        const apiError = unknownError as any; // Type assertion for property access
        errorMessage = `Twitter API Error: ${apiError.data && typeof apiError.data === 'object'
            ? (apiError.data.detail || apiError.data.title || 'API Error')
            : 'Unknown Twitter API error'
          }`;
        errorCode = `HTTP_${apiError.code || 'UNKNOWN'}`;
      }

      // Twitter Request errors
      if (ApiRequestError && unknownError instanceof ApiRequestError) {
        const requestError = unknownError as Error;
        errorMessage = `Twitter Request Error: ${requestError.message}`;
        errorCode = 'REQUEST_ERROR';
      }

      return {
        success: false,
        error: {
          message: errorMessage,
          code: errorCode
        }
      };
    }
  }

  public async uploadMedia(file: UploadedFile, twitterAccount: TwitterAccount): Promise<string> {
    try {
      await this.waitForRateLimit(this.mediaLimiter);

      const client = await this.getTwitterClient(twitterAccount);

      const response = await fetch(file.fileURL);
      if (!response.ok) {
        throw new Error('Failed to fetch media file');
      }

      const buffer = await response.arrayBuffer();

      // Upload media using v1 API
      const mediaId = await client.v1.uploadMedia(Buffer.from(buffer), {
        mimeType: file.fileType
      });

      return mediaId;

    } catch (error) {
      console.error('Media upload error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to upload media');
    }
  }
}

// Export singleton instance - lazy initialization to avoid circular dependencies
let _twitterPublisher: TwitterPublisher | null = null;

export function getTwitterPublisher(): TwitterPublisher {
  if (!_twitterPublisher) {
    _twitterPublisher = new TwitterPublisher();
  }
  return _twitterPublisher;
}

// For backward compatibility
export const twitterPublisher = getTwitterPublisher();