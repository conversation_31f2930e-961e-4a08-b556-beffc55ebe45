// Server initialization - runs when the server starts
import { cronManager } from './cron';

let isInitialized = false;

export async function initializeServer() {
  if (isInitialized) {
    return;
  }

  console.log('Initializing server components...');

  try {
    // Initialize cron jobs
    await cronManager.initialize();
    
    isInitialized = true;
    console.log('Server initialization completed successfully');
  } catch (error) {
    console.error('Server initialization failed:', error);
  }
}

// Auto-initialize when this module is imported on the server side
if (typeof window === 'undefined') {
  initializeServer().catch(console.error);
}
