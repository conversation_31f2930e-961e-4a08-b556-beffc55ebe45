import crypto from 'crypto';

// Encryption key should be 32 bytes (256 bits) for AES-256
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-dev-key-please-change-in-production';
// IV length for AES-256-GCM
const IV_LENGTH = 16;
// Auth tag length
const AUTH_TAG_LENGTH = 16;

/**
 * Encrypts sensitive data using AES-256-GCM
 * @param text The plaintext to encrypt
 * @returns Buffer containing IV + Auth Tag + Encrypted data
 */
export function encrypt(text: string): Buffer {
  // Generate a random initialization vector
  const iv = crypto.randomBytes(IV_LENGTH);
  
  // Create cipher with key, iv, and specify auth tag length
  const cipher = crypto.createCipheriv(
    'aes-256-gcm', 
    Buffer.from(ENCRYPTION_KEY.slice(0, 32)), 
    iv,
    { authTagLength: AUTH_TAG_LENGTH }
  );
  
  // Encrypt the data
  let encrypted = cipher.update(text, 'utf8');
  encrypted = Buffer.concat([encrypted, cipher.final()]);
  
  // Get the auth tag
  const authTag = cipher.getAuthTag();
  
  // Return iv + authTag + encrypted data
  // Format: [16 bytes IV][16 bytes Auth Tag][n bytes encrypted data]
  return Buffer.concat([iv, authTag, encrypted]);
}

/**
 * Decrypts data that was encrypted with the encrypt function
 * @param encryptedData Buffer containing IV + Auth Tag + Encrypted data
 * @returns The decrypted plaintext
 */
export function decrypt(encryptedData: Buffer): string {
  try {
    // Extract the IV from the first 16 bytes
    const iv = encryptedData.slice(0, IV_LENGTH);
    
    // Extract the auth tag from the next 16 bytes
    const authTag = encryptedData.slice(IV_LENGTH, IV_LENGTH + AUTH_TAG_LENGTH);
    
    // Extract the encrypted text from the remaining bytes
    const encryptedText = encryptedData.slice(IV_LENGTH + AUTH_TAG_LENGTH);
    
    // Create decipher
    const decipher = crypto.createDecipheriv(
      'aes-256-gcm',
      Buffer.from(ENCRYPTION_KEY.slice(0, 32)),
      iv,
      { authTagLength: AUTH_TAG_LENGTH }
    );
    
    // Set auth tag
    decipher.setAuthTag(authTag);
    
    // Decrypt the data
    let decrypted = decipher.update(encryptedText);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    // Return the decrypted text
    return decrypted.toString('utf8');
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data. The data may be corrupted or the encryption key may be invalid.');
  }
}

