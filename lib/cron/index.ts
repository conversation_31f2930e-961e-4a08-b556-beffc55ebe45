import * as schedule from 'node-schedule';

interface CronJob {
  name: string;
  schedule: string;
  task: () => Promise<void>;
  running: boolean;
}

class CronManager {
  private jobs: Map<string, schedule.Job> = new Map();
  private isInitialized = false;

  async initialize() {
    if (this.isInitialized) {
      console.log('Cron manager already initialized');
      return;
    }

    console.log('Initializing cron jobs...');

    // Only run cron jobs in production or when explicitly enabled
    const shouldRunCron = process.env.NODE_ENV === 'production' || process.env.ENABLE_CRON === 'true';
    
    if (!shouldRunCron) {
      console.log('Cron jobs disabled in development environment');
      return;
    }

    try {
      // Schedule the tweet publishing job
      await this.scheduleJob({
        name: 'publish-scheduled-tweets',
        schedule: '*/2 * * * *', // Every 2 minutes (cron format)
        task: this.publishScheduledTweets,
        running: false
      });

      this.isInitialized = true;
      console.log('Cron jobs initialized successfully');
    } catch (error) {
      console.error('Failed to initialize cron jobs:', error);
    }
  }

  private async scheduleJob(jobConfig: CronJob) {
    const { name, schedule: cronSchedule, task } = jobConfig;

    if (this.jobs.has(name)) {
      console.log(`Cron job '${name}' already exists, skipping...`);
      return;
    }

    const scheduledTask = schedule.scheduleJob(name, cronSchedule, async () => {
      if (jobConfig.running) {
        console.log(`Cron job '${name}' is already running, skipping...`);
        return;
      }

      jobConfig.running = true;
      const startTime = Date.now();

      try {
        console.log(`Starting cron job: ${name}`);
        await task();
        const duration = Date.now() - startTime;
        console.log(`Cron job '${name}' completed in ${duration}ms`);
      } catch (error) {
        console.error(`Cron job '${name}' failed:`, error);
      } finally {
        jobConfig.running = false;
      }
    });

    if (scheduledTask) {
      this.jobs.set(name, scheduledTask);
      console.log(`Scheduled cron job '${name}' with schedule: ${cronSchedule}`);
    } else {
      throw new Error(`Failed to schedule job '${name}' with schedule: ${cronSchedule}`);
    }
  }

  private async publishScheduledTweets() {
    try {
      // Call the API endpoint internally
      const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/cron/publish-scheduled-tweets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'CronJob/1.0'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Scheduled tweets publishing result:', result);
    } catch (error) {
      console.error('Error calling scheduled tweets API:', error);
      throw error;
    }
  }

  async stopJob(name: string) {
    const job = this.jobs.get(name);
    if (job) {
      job.cancel();
      this.jobs.delete(name);
      console.log(`Stopped cron job: ${name}`);
    }
  }

  async stopAll() {
    console.log('Stopping all cron jobs...');
    Array.from(this.jobs.entries()).forEach(([name, job]) => {
      job.cancel();
      console.log(`Stopped cron job: ${name}`);
    });
    this.jobs.clear();
    this.isInitialized = false;
  }

  getJobStatus(): { name: string; running: boolean }[] {
    return Array.from(this.jobs.keys()).map(name => ({
      name,
      running: false // node-schedule doesn't have a running property, so we'll track this differently if needed
    }));
  }
}

// Export singleton instance
export const cronManager = new CronManager();

// Initialize cron jobs when this module is imported
if (typeof window === 'undefined') { // Only run on server side
  cronManager.initialize().catch(console.error);
}
