// Simple runtime configuration
function getBaseUrl() {
  // Always use localhost for development
  if (process.env.NODE_ENV === 'development') {
    const port = process.env.PORT || '3030';
    return `http://localhost:${port}`;
  }

  // Use NEXT_PUBLIC_BASE_URL if set (primary) for production/other environments
  if (process.env.NEXT_PUBLIC_BASE_URL) {
    return process.env.NEXT_PUBLIC_BASE_URL;
  }

  // Fallback for other environments if NEXT_PUBLIC_BASE_URL is not set
  // This might indicate a misconfiguration in production, but provides a default
  return `http://localhost:${process.env.PORT || '3030'}`;
}

const baseUrl = getBaseUrl();

export const config = {
  app: {
    baseUrl,
  },
  auth: {
    jwt: {
      secret: process.env.JWT_SECRET!,
      expiresIn: '7d',
    },
    session: {
      secret: process.env.SESSION_SECRET!,
      cookieName: 'auth_token',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    },
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      callbackUrl: `${baseUrl}/api/auth/google/callback`,
    },
    twitter: {
      clientId: process.env.TWITTER_API_KEY!,
      clientSecret: process.env.TWITTER_API_SECRET!,
      callbackUrl: `${baseUrl}/api/auth/twitter/callback`,
    },
  },
};

// Debug logging (only in development)
if (process.env.NODE_ENV === 'development') {
  console.log('Config:', {
    NODE_ENV: process.env.NODE_ENV,
    baseUrl,
    NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
  });
}
