# Environment Configuration Guide

## Overview

This document explains how to properly configure environment variables for different deployment scenarios to avoid hardcoded domains and localhost issues in production.

## Key Environment Variables

### Base URL Configuration

The application uses a hierarchical approach to determine the base URL:

1. **NEXT_PUBLIC_BASE_URL** (Primary) - Used for OAuth callbacks and redirects
2. **NEXTAUTH_URL** (Fallback) - For NextAuth.js compatibility
3. **Automatic fallback** - Based on NODE_ENV and PORT

### Environment Files

#### Development (.env.local or .env)
```bash
NODE_ENV=development
PORT=3030
NEXT_PUBLIC_BASE_URL=http://localhost:3030
NEXTAUTH_URL=http://localhost:3030
```

#### Production (.env.production or .env)
```bash
NODE_ENV=production
PORT=3030
NEXT_PUBLIC_BASE_URL=https://tasker.violetmethods.com
NEXTAUTH_URL=https://tasker.violetmethods.com
```

#### Docker/Container Deployment
```bash
NODE_ENV=production
PORT=3030
NEXT_PUBLIC_BASE_URL=https://your-domain.com
NEXTAUTH_URL=https://your-domain.com
```

## OAuth Configuration

### Google OAuth
- Callback URL: `${NEXT_PUBLIC_BASE_URL}/api/auth/google/callback`
- Configure in Google Cloud Console with the exact callback URL

### Twitter OAuth
- Callback URL: `${NEXT_PUBLIC_BASE_URL}/api/auth/twitter/callback`
- Configure in Twitter Developer Portal with the exact callback URL

## Deployment Scripts

### Standalone Deployment (Default - Recommended)
```bash
npm run build
npm run start
```
*Note: Uses `node .next/standalone/server.js` automatically*

### Traditional Next.js Server (Alternative)
```bash
npm run build
npm run start:next
```
*Note: Uses `next start` - only if standalone is disabled*

### Development
```bash
npm run dev
```

## Nginx Configuration

For nginx proxy setups, ensure your configuration matches:

```nginx
server {
    listen 80;
    server_name tasker.violetmethods.com;
    
    location / {
        proxy_pass http://localhost:3030;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Troubleshooting

### Common Issues

1. **OAuth callbacks failing**: Ensure NEXT_PUBLIC_BASE_URL matches the domain configured in OAuth providers
2. **Localhost in production**: Verify NODE_ENV=production and NEXT_PUBLIC_BASE_URL is set
3. **Port binding issues**: Use `--hostname 0.0.0.0` for external access

### Verification

Check the console output on startup for configuration details:
```
🔧 Config loaded: {
  NODE_ENV: 'production',
  baseUrl: 'https://tasker.violetmethods.com',
  googleCallbackUrl: 'https://tasker.violetmethods.com/api/auth/google/callback',
  twitterCallbackUrl: 'https://tasker.violetmethods.com/api/auth/twitter/callback',
  usingEnvVar: 'NEXT_PUBLIC_BASE_URL'
}
```

## Security Notes

- Never commit real API keys to version control
- Use different OAuth credentials for development and production
- Ensure HTTPS in production for OAuth callbacks
- Rotate secrets regularly
