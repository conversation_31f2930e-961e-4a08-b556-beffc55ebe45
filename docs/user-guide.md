# Persona Management System User Guide

## Introduction

The Persona Management System allows you to create, edit, import, export, and switch between different bot personas. This guide will walk you through all the features and functionality of the system.

## What are Personas?

Personas are predefined personalities and characteristics that your bot can adopt. Each persona includes:

- **Name**: A unique identifier for the persona
- **System Prompt**: The main instructions that guide the bot's behavior
- **Bio**: Background information about the persona
- **Topics**: Subjects the persona is knowledgeable about
- **Style**: Writing style guidelines for different content types
- **Message Examples**: Sample conversations demonstrating the persona's voice
- **Adjectives**: Descriptive words that define the persona's character

## Getting Started

### Accessing Persona Management

You can access the Persona Management System from:

1. The main dashboard via the Persona Selector Panel
2. The header menu via the Persona Header Selector
3. Directly at `/bots/personas`

### Creating a New Persona

To create a new persona:

1. Navigate to the Personas page
2. Click the "New Persona" button
3. Fill out the required fields in the Persona Editor:
   - **Basic Info**: Name and bio
   - **Content**: System prompt and message examples
   - **Style & Topics**: Adjectives, topics, and writing style
4. Click "Save" to create your new persona

### Editing an Existing Persona

To edit a persona:

1. Find the persona in the grid view
2. Click the "Edit" button (pencil icon)
3. Make your changes in the Persona Editor
4. Click "Save" to update the persona

### Deleting a Persona

To delete a persona:

1. Find the persona in the grid view
2. Click the "Delete" button (trash icon)
3. Confirm the deletion when prompted

## Advanced Features

### Importing Personas

You can import personas from JSON files:

1. Click the "Import Persona" button on the Personas page
2. Either:
   - Drag and drop a JSON file onto the upload area
   - Click "Select File" to browse for a JSON file
3. The system will validate and import the persona

### Exporting Personas

To export a persona:

1. Find the persona in the grid view
2. Click the "Export" button (download icon)
3. Choose to either:
   - Copy the JSON data to clipboard
   - Download as a JSON file

### Setting an Active Persona

The active persona is used by default for all bot interactions:

1. Find the persona you want to activate
2. Click the "Set Active" button
3. The persona will now be highlighted and marked as "Active"

You can also set the active persona from:
- The Persona Header Selector in the top navigation
- The Persona Selector Panel in the dashboard

### Switching Between Personas

To quickly switch between personas:

1. Click the Persona Selector in the header
2. Select a persona from the dropdown menu
3. The system will immediately switch to the selected persona

## Persona Format

Each persona includes the following fields:

| Field | Description | Required |
|-------|-------------|----------|
| name | The name of the persona | Yes |
| system | Main system prompt for the AI | Yes |
| bio | Array of background information | No |
| lore | Array of additional background details | No |
| messageExamples | Sample conversations | No |
| postExamples | Sample social media posts | No |
| adjectives | Descriptive words for the persona | No |
| topics | Subjects the persona knows about | No |
| style | Writing style guidelines | No |
| version | Format version number | No |

## Tips and Best Practices

- **Create Specific Personas**: More specific personas tend to produce more consistent results
- **Use Message Examples**: Providing examples helps the AI understand the desired tone and style
- **Test Your Personas**: Always test your personas before using them in production
- **Regular Updates**: Periodically review and update your personas to improve their performance
- **Organize by Purpose**: Create different personas for different use cases (e.g., customer service, marketing, technical support)

## Troubleshooting

### Import Errors

If you encounter errors when importing a persona:

1. Ensure the JSON file is properly formatted
2. Check that all required fields are present
3. Verify that the file is not corrupted

### Persona Not Appearing

If a newly created persona doesn't appear in the list:

1. Refresh the page
2. Check if there are any error messages
3. Verify that the persona was saved successfully

### Active Persona Not Working

If the active persona isn't being used:

1. Verify that the persona is marked as active
2. Check if there are any error messages in the console
3. Try setting a different persona as active, then switch back

## Getting Help

If you need additional assistance:

- Check the developer documentation for technical details
- Contact support through the help center
- Submit a bug report if you encounter issues

