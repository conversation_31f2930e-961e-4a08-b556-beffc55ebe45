# Deployment Options Guide

## 🚀 **Recommended: Traditional Next.js Server (Full UI Support)**

Your application is now configured to use the traditional Next.js server which includes full UI support.

### Quick Start
```bash
# Build for production
npm run build

# Start production server (with full UI)
npm run start
```

## 🔧 **Alternative: Standalone Deployment (Advanced)**

If you need standalone deployment for specific containerization requirements:

### Build with Static Assets
```bash
# Build standalone with static files copied
npm run build:standalone
```

### Start Standalone Server
```bash
npm run start:standalone
```

## 📋 Available Scripts

### Production Scripts
```bash
# Default production start (standalone)
npm run start

# Alternative: Traditional Next.js server (if standalone disabled)
npm run start:next

# Explicit standalone start
npm run start:standalone
```

### Development Scripts
```bash
# Development server
npm run dev

# Alternative development command
npm run start:dev
```

## 🔧 Configuration Details

### next.config.js
```javascript
module.exports = {
  // Automatically enables standalone in production
  output: process.env.NODE_ENV === 'production' ? 'standalone' : undefined,
  // ... other config
};
```

### package.json Scripts
```json
{
  "scripts": {
    "start": "NODE_ENV=production node .next/standalone/server.js",
    "start:next": "NODE_ENV=production next start --hostname 0.0.0.0 --port ${PORT:-3030}",
    "start:standalone": "NODE_ENV=production node .next/standalone/server.js"
  }
}
```

## 🐳 Docker Deployment

### Dockerfile Example
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy standalone build
COPY .next/standalone ./
COPY .next/static ./.next/static
COPY public ./public

EXPOSE 3030

CMD ["node", "server.js"]
```

### Docker Commands
```bash
# Build the Next.js app
npm run build

# Build Docker image
docker build -t xsche .

# Run container
docker run -p 3030:3030 xsche
```

## 🌐 Environment Variables

### Required for Production
```bash
NODE_ENV=production
PORT=3030
NEXT_PUBLIC_BASE_URL=https://your-domain.com
```

### Server Binding
The standalone server automatically binds to:
- **Host**: `0.0.0.0` (accepts external connections)
- **Port**: `process.env.PORT || 3030`

## 🔍 Troubleshooting

### Issue: "next start" doesn't work
```bash
# ❌ This will fail with standalone output
npm run start:next

# ✅ Use this instead
npm run start
```

### Issue: Server not accessible externally
The standalone server automatically binds to `0.0.0.0`, so this shouldn't be an issue. If you have problems:

1. Check firewall settings
2. Verify PORT environment variable
3. Ensure NEXT_PUBLIC_BASE_URL is correct

### Issue: Static files not loading
Ensure your build includes static files:
```bash
# After npm run build, verify these exist:
ls -la .next/standalone/
ls -la .next/static/
```

## 📊 Performance Benefits

### Standalone vs Traditional
- ✅ **Faster cold starts**
- ✅ **Smaller bundle size**
- ✅ **Better for containers**
- ✅ **Optimized for serverless**

### Bundle Analysis
```bash
# Check bundle size after build
du -sh .next/standalone/
du -sh .next/static/
```

## 🚀 Deployment Platforms

### Vercel
```bash
# Vercel automatically handles standalone builds
vercel deploy
```

### Railway/Render
```bash
# Build command
npm run build

# Start command
npm run start
```

### AWS/GCP/Azure
```bash
# Use standalone server
node .next/standalone/server.js
```

## ✅ Verification

After deployment, verify your app is running:

```bash
# Check if server is responding
curl -I http://localhost:3030

# Check OAuth callbacks
curl -I http://localhost:3030/api/auth/google/callback
curl -I http://localhost:3030/api/auth/twitter/callback
```

## 📝 Notes

1. **Standalone output** is automatically enabled in production
2. **Static files** are included in the build
3. **Environment variables** are read at runtime
4. **OAuth callbacks** work with dynamic base URLs
5. **Database connections** use connection pooling

Your application is optimized for modern deployment platforms and follows Next.js best practices for production deployments.
