# UI Component Usage Guidelines

## Overview

This document provides guidelines for using the UI components in the xsche application. These components are built using React, Tailwind CSS, and shadcn/ui, and are designed to provide a consistent user experience across the application.

## Component Library Structure

The UI components are organized into the following categories:

1. **Core Components**: Basic building blocks like buttons, inputs, and cards
2. **Composite Components**: More complex components composed of multiple core components
3. **Layout Components**: Components for structuring the page layout
4. **Specialized Components**: Components specific to the xsche application domain

## Core Components

### Button

The Button component is used for actions and navigation.

```tsx
import { Button } from "@/components/ui/button";

// Primary button (default)
<Button>Click Me</Button>

// Secondary button
<Button variant="secondary">Secondary</Button>

// Outline button
<Button variant="outline">Outline</Button>

// Ghost button
<Button variant="ghost">Ghost</Button>

// Destructive button
<Button variant="destructive">Delete</Button>

// Link button
<Button variant="link">Link</Button>

// Disabled button
<Button disabled>Disabled</Button>

// Button with icon
<Button>
  <PlusIcon className="mr-2 h-4 w-4" />
  Add Item
</Button>

// Button sizes
<Button size="sm">Small</Button>
<Button size="default">Default</Button>
<Button size="lg">Large</Button>
```

### Input

The Input component is used for text input.

```tsx
import { Input } from "@/components/ui/input";

// Basic input
<Input placeholder="Enter your name" />

// Disabled input
<Input disabled placeholder="Disabled" />

// Input with label
<div className="grid w-full max-w-sm items-center gap-1.5">
  <Label htmlFor="email">Email</Label>
  <Input type="email" id="email" placeholder="Email" />
</div>

// Input with error
<div className="grid w-full max-w-sm items-center gap-1.5">
  <Label htmlFor="email-error">Email</Label>
  <Input
    type="email"
    id="email-error"
    placeholder="Email"
    className="border-red-500"
  />
  <p className="text-sm text-red-500">Please enter a valid email address.</p>
</div>
```

### Card

The Card component is used for displaying content in a contained area.

```tsx
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card Description</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Card Content</p>
  </CardContent>
  <CardFooter>
    <p>Card Footer</p>
  </CardFooter>
</Card>
```

## Composite Components

### PersonaCard

The PersonaCard component is used for displaying persona information.

```tsx
import { PersonaCard } from "@/components/bots/persona-card";

<PersonaCard
  persona={persona}
  onSelect={() => handleSelect(persona)}
  onEdit={() => handleEdit(persona)}
  onDelete={() => handleDelete(persona)}
  onExport={() => handleExport(persona)}
  isActive={activePersonaId === persona.id}
/>
```

### PersonaForm

The PersonaForm component is used for creating and editing personas.

```tsx
import { PersonaForm } from "@/components/bots/persona-form";

<PersonaForm
  initialValues={persona}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
/>
```

### PersonaGrid

The PersonaGrid component is used for displaying a grid of personas with filtering and pagination.

```tsx
import { PersonaGrid } from "@/components/bots/persona-grid";

<PersonaGrid
  personas={personas}
  activePersonaId={activePersonaId}
  onSelect={handleSelect}
  onEdit={handleEdit}
  onDelete={handleDelete}
  onExport={handleExport}
  onCreateNew={handleCreateNew}
  onSearch={handleSearch}
  onSortChange={handleSortChange}
  onPageChange={handlePageChange}
  isLoading={isLoading}
  page={page}
  totalPages={totalPages}
/>
```

## Layout Components

### PageLayout

The PageLayout component is used for structuring the page layout.

```tsx
import { PageLayout } from "@/components/layout/page-layout";

<PageLayout>
  <PageLayout.Header>
    <h1>Page Title</h1>
  </PageLayout.Header>
  <PageLayout.Content>
    <p>Page content goes here</p>
  </PageLayout.Content>
  <PageLayout.Footer>
    <p>Page footer goes here</p>
  </PageLayout.Footer>
</PageLayout>
```

### SidebarLayout

The SidebarLayout component is used for creating a layout with a sidebar.

```tsx
import { SidebarLayout } from "@/components/layout/sidebar-layout";

<SidebarLayout>
  <SidebarLayout.Sidebar>
    <nav>
      <ul>
        <li>Menu Item 1</li>
        <li>Menu Item 2</li>
      </ul>
    </nav>
  </SidebarLayout.Sidebar>
  <SidebarLayout.Content>
    <p>Main content goes here</p>
  </SidebarLayout.Content>
</SidebarLayout>
```

## Specialized Components

### PersonaImportExport

The PersonaImportExport component is used for importing and exporting personas.

```tsx
import { PersonaImportExport } from "@/components/bots/persona-import-export";

<PersonaImportExport
  onImport={handleImport}
  onExport={handleExport}
  persona={persona}
/>
```

### PersonaTemplateSelector

The PersonaTemplateSelector component is used for selecting a persona template.

```tsx
import { PersonaTemplateSelector } from "@/components/bots/persona-template-selector";

<PersonaTemplateSelector
  templates={templates}
  onSelect={handleSelectTemplate}
  isLoading={isLoading}
/>
```

## Theming

The UI components support theming through CSS variables. The default theme is defined in the `globals.css` file.

```css
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 48%;
}
```

## Accessibility

All UI components are designed with accessibility in mind. They follow these principles:

1. **Keyboard Navigation**: All interactive elements are keyboard accessible
2. **Screen Reader Support**: Appropriate ARIA attributes are used
3. **Color Contrast**: Color combinations meet WCAG 2.1 AA standards
4. **Focus Management**: Focus states are clearly visible

## Best Practices

### Component Selection

1. Use the appropriate component for the task
2. Prefer existing components over creating new ones
3. Compose complex UIs from simpler components

### Component Customization

1. Use the provided props for customization
2. Use the `className` prop for additional styling
3. Avoid direct DOM manipulation

### Performance Considerations

1. Use memoization for expensive computations
2. Avoid unnecessary re-renders
3. Use virtualization for long lists

### Error Handling

1. Provide clear error messages
2. Use form validation
3. Handle loading and error states

## Component Development

When developing new components:

1. Follow the existing component structure
2. Use TypeScript for type safety
3. Document props and usage examples
4. Write unit tests
5. Consider accessibility from the start

## Example Patterns

### Form with Validation

```tsx
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const formSchema = z.object({
  username: z.string().min(2).max(50),
  email: z.string().email(),
});

export function ProfileForm() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: "",
      email: "",
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Username</FormLabel>
              <FormControl>
                <Input placeholder="johndoe" {...field} />
              </FormControl>
              <FormDescription>
                This is your public display name.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormDescription>
                We'll never share your email with anyone else.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit">Submit</Button>
      </form>
    </Form>
  );
}
```

### Data Table with Pagination

```tsx
import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";

interface DataTableProps<T> {
  data: T[];
  columns: {
    key: keyof T;
    header: string;
    cell?: (item: T) => React.ReactNode;
  }[];
  itemsPerPage?: number;
}

export function DataTable<T>({
  data,
  columns,
  itemsPerPage = 10,
}: DataTableProps<T>) {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(data.length / itemsPerPage);

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = data.slice(startIndex, endIndex);

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            {columns.map((column) => (
              <TableHead key={column.key as string}>{column.header}</TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {currentData.map((item, index) => (
            <TableRow key={index}>
              {columns.map((column) => (
                <TableCell key={column.key as string}>
                  {column.cell
                    ? column.cell(item)
                    : (item[column.key] as React.ReactNode)}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
          disabled={currentPage === 1}
        >
          Previous
        </Button>
        <div className="text-sm">
          Page {currentPage} of {totalPages}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() =>
            setCurrentPage((prev) => Math.min(prev + 1, totalPages))
          }
          disabled={currentPage === totalPages}
        >
          Next
        </Button>
      </div>
    </div>
  );
}
```

## Resources

- [Component Source Code](https://github.com/lord-dubious/xsche/tree/main/components)
- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [Accessibility Guidelines (WCAG 2.1)](https://www.w3.org/WAI/WCAG21/quickref/)

