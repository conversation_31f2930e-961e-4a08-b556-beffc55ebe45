# Persona Format Specification

## Overview

A persona in the xsche system is a structured representation of a bot's personality, behavior, and communication style. This document provides a comprehensive specification of the persona format used throughout the application.

## Schema

Personas are stored as JSON objects with the following structure:

```typescript
interface Persona {
  id: string;                 // Unique identifier
  userId: string;             // Owner of the persona
  name: string;               // Display name
  system: string;             // System prompt/instructions
  bio: string[];              // Array of biographical details
  style: {
    all: string[];            // Style instructions for all contexts
    chat: string[];           // Style instructions for chat contexts
    post: string[];           // Style instructions for post contexts
  };
  createdAt: Date;            // Creation timestamp
  updatedAt: Date;            // Last update timestamp
}
```

## Field Descriptions

### Required Fields

| Field | Type | Description |
|-------|------|-------------|
| `name` | string | The display name of the persona. Should be concise and descriptive. |
| `system` | string | The system prompt/instructions that define the persona's core behavior and knowledge. |

### Optional Fields

| Field | Type | Description |
|-------|------|-------------|
| `bio` | string[] | An array of biographical details about the persona. Each element is a separate aspect of the persona's background or character. |
| `style.all` | string[] | Style instructions that apply to all contexts. |
| `style.chat` | string[] | Style instructions specific to conversational contexts. |
| `style.post` | string[] | Style instructions specific to content creation contexts. |

### System-Managed Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Unique identifier generated by the system. |
| `userId` | string | The ID of the user who owns this persona. |
| `createdAt` | Date | Timestamp when the persona was created. |
| `updatedAt` | Date | Timestamp when the persona was last updated. |

## Serialization Format

When personas are serialized for storage or transmission, they follow these rules:

1. All dates are converted to ISO 8601 format strings
2. Arrays are preserved as arrays
3. Nested objects maintain their structure
4. No circular references are allowed

Example of a serialized persona:

```json
{
  "id": "persona_12345",
  "userId": "user_67890",
  "name": "Technical Expert",
  "system": "You are a technical expert who provides clear, accurate information about software development.",
  "bio": [
    "10+ years of software development experience",
    "Specializes in JavaScript and TypeScript",
    "Known for clear explanations of complex topics"
  ],
  "style": {
    "all": [
      "Use technical terminology appropriately",
      "Provide code examples when helpful"
    ],
    "chat": [
      "Ask clarifying questions when the request is ambiguous",
      "Summarize complex explanations at the end"
    ],
    "post": [
      "Structure content with clear headings",
      "Include a TL;DR at the beginning for complex topics"
    ]
  },
  "createdAt": "2023-06-15T14:30:00.000Z",
  "updatedAt": "2023-06-16T09:45:00.000Z"
}
```

## Validation Rules

When creating or updating personas, the following validation rules apply:

1. `name` must be a non-empty string with a maximum length of 100 characters
2. `system` must be a non-empty string with a maximum length of 10,000 characters
3. `bio` must be an array of strings, each with a maximum length of 500 characters
4. Style instructions must be arrays of strings, each with a maximum length of 500 characters
5. The total size of a serialized persona must not exceed 100KB

## Import/Export Format

When personas are exported for sharing or backup, they use a slightly modified format:

```typescript
interface ExportedPersona {
  name: string;
  system: string;
  bio: string[];
  style: {
    all: string[];
    chat: string[];
    post: string[];
  };
  metadata: {
    version: string;
    exportedAt: string;
    source: string;
  };
}
```

The `metadata` field contains information about the export itself, including:
- `version`: The version of the export format
- `exportedAt`: Timestamp when the export was created
- `source`: Identifier for the system that created the export

## Template Format

Persona templates follow the same structure as regular personas but may include placeholder values that are replaced when a new persona is created from the template:

```typescript
interface PersonaTemplate extends Omit<Persona, 'id' | 'userId' | 'createdAt' | 'updatedAt'> {
  templateId: string;
  description: string;
  placeholders: {
    [key: string]: {
      description: string;
      defaultValue: string;
    };
  };
}
```

Placeholders in the template text are denoted by `{{placeholder_name}}` syntax.

## Best Practices

When creating personas:

1. Keep the system prompt focused and specific
2. Use the bio field to establish character traits and background
3. Separate style instructions by context (all, chat, post)
4. Avoid contradictory instructions
5. Test personas with a variety of inputs to ensure consistent behavior

## Version History

- **v1.0.0** (2023-06-01): Initial specification
- **v1.1.0** (2023-08-15): Added style.post field
- **v1.2.0** (2023-10-30): Added template format
- **v1.3.0** (2024-01-15): Increased system prompt maximum length to 10,000 characters

