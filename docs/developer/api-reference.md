# API Reference

## Overview

The xsche API provides programmatic access to persona management and related functionality. This document outlines the available endpoints, request/response formats, and authentication requirements.

## Base URL

All API endpoints are relative to the base URL:

```
https://api.xsche.com/v1
```

For local development, use:

```
http://localhost:3000/api
```

## Authentication

### API Keys

Most API endpoints require authentication using an API key. Include your API key in the request headers:

```
Authorization: Bearer YOUR_API_KEY
```

API keys can be generated and managed in the user dashboard.

### OAuth 2.0

For integrations that act on behalf of users, OAuth 2.0 authentication is supported:

1. Redirect users to `/auth/oauth/authorize` with your client ID and redirect URI
2. Users authorize your application
3. Users are redirected back to your application with an authorization code
4. Exchange the code for an access token using `/auth/oauth/token`
5. Include the access token in API requests

## Rate Limiting

API requests are subject to rate limiting:

- 100 requests per minute per API key
- 1000 requests per day per API key

Rate limit information is included in the response headers:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1623456789
```

## Endpoints

### Personas

#### List Personas

```
GET /personas
```

Retrieves a list of personas owned by the authenticated user.

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `page` | integer | Page number (default: 1) |
| `limit` | integer | Items per page (default: 10, max: 100) |
| `sort` | string | Sort field (name, createdAt, updatedAt) |
| `order` | string | Sort order (asc, desc) |
| `search` | string | Search term for filtering by name or system prompt |

**Response:**

```json
{
  "personas": [
    {
      "id": "persona_12345",
      "name": "Technical Expert",
      "system": "You are a technical expert...",
      "bio": ["10+ years of software development experience", "..."],
      "style": {
        "all": ["Use technical terminology appropriately", "..."],
        "chat": ["Ask clarifying questions when the request is ambiguous", "..."],
        "post": ["Structure content with clear headings", "..."]
      },
      "createdAt": "2023-06-15T14:30:00.000Z",
      "updatedAt": "2023-06-16T09:45:00.000Z"
    },
    // More personas...
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalCount": 25,
    "totalPages": 3
  }
}
```

#### Get Persona

```
GET /personas/:id
```

Retrieves a specific persona by ID.

**Response:**

```json
{
  "persona": {
    "id": "persona_12345",
    "name": "Technical Expert",
    "system": "You are a technical expert...",
    "bio": ["10+ years of software development experience", "..."],
    "style": {
      "all": ["Use technical terminology appropriately", "..."],
      "chat": ["Ask clarifying questions when the request is ambiguous", "..."],
      "post": ["Structure content with clear headings", "..."]
    },
    "createdAt": "2023-06-15T14:30:00.000Z",
    "updatedAt": "2023-06-16T09:45:00.000Z"
  }
}
```

#### Create Persona

```
POST /personas
```

Creates a new persona.

**Request Body:**

```json
{
  "name": "Technical Expert",
  "system": "You are a technical expert...",
  "bio": ["10+ years of software development experience", "..."],
  "style": {
    "all": ["Use technical terminology appropriately", "..."],
    "chat": ["Ask clarifying questions when the request is ambiguous", "..."],
    "post": ["Structure content with clear headings", "..."]
  }
}
```

**Response:**

```json
{
  "persona": {
    "id": "persona_12345",
    "name": "Technical Expert",
    "system": "You are a technical expert...",
    "bio": ["10+ years of software development experience", "..."],
    "style": {
      "all": ["Use technical terminology appropriately", "..."],
      "chat": ["Ask clarifying questions when the request is ambiguous", "..."],
      "post": ["Structure content with clear headings", "..."]
    },
    "createdAt": "2023-06-15T14:30:00.000Z",
    "updatedAt": "2023-06-15T14:30:00.000Z"
  }
}
```

#### Update Persona

```
PUT /personas/:id
```

Updates an existing persona.

**Request Body:**

```json
{
  "name": "Updated Technical Expert",
  "system": "You are a technical expert...",
  "bio": ["10+ years of software development experience", "..."],
  "style": {
    "all": ["Use technical terminology appropriately", "..."],
    "chat": ["Ask clarifying questions when the request is ambiguous", "..."],
    "post": ["Structure content with clear headings", "..."]
  }
}
```

**Response:**

```json
{
  "persona": {
    "id": "persona_12345",
    "name": "Updated Technical Expert",
    "system": "You are a technical expert...",
    "bio": ["10+ years of software development experience", "..."],
    "style": {
      "all": ["Use technical terminology appropriately", "..."],
      "chat": ["Ask clarifying questions when the request is ambiguous", "..."],
      "post": ["Structure content with clear headings", "..."]
    },
    "createdAt": "2023-06-15T14:30:00.000Z",
    "updatedAt": "2023-06-16T09:45:00.000Z"
  }
}
```

#### Delete Persona

```
DELETE /personas/:id
```

Deletes a persona.

**Response:**

```json
{
  "success": true,
  "message": "Persona deleted successfully"
}
```

#### Import Persona

```
POST /personas/import
```

Imports a persona from a JSON file.

**Request Body:**

```json
{
  "persona": {
    "name": "Imported Persona",
    "system": "You are an imported persona...",
    "bio": ["Imported from another system", "..."],
    "style": {
      "all": ["Maintain consistent style", "..."],
      "chat": ["Be conversational", "..."],
      "post": ["Be formal", "..."]
    },
    "metadata": {
      "version": "1.0.0",
      "exportedAt": "2023-06-15T14:30:00.000Z",
      "source": "external-system"
    }
  }
}
```

**Response:**

```json
{
  "persona": {
    "id": "persona_67890",
    "name": "Imported Persona",
    "system": "You are an imported persona...",
    "bio": ["Imported from another system", "..."],
    "style": {
      "all": ["Maintain consistent style", "..."],
      "chat": ["Be conversational", "..."],
      "post": ["Be formal", "..."]
    },
    "createdAt": "2023-06-16T10:00:00.000Z",
    "updatedAt": "2023-06-16T10:00:00.000Z"
  }
}
```

#### Export Persona

```
GET /personas/:id/export
```

Exports a persona in a format suitable for sharing or backup.

**Response:**

```json
{
  "persona": {
    "name": "Technical Expert",
    "system": "You are a technical expert...",
    "bio": ["10+ years of software development experience", "..."],
    "style": {
      "all": ["Use technical terminology appropriately", "..."],
      "chat": ["Ask clarifying questions when the request is ambiguous", "..."],
      "post": ["Structure content with clear headings", "..."]
    },
    "metadata": {
      "version": "1.0.0",
      "exportedAt": "2023-06-16T11:15:00.000Z",
      "source": "xsche"
    }
  }
}
```

### Templates

#### List Templates

```
GET /templates
```

Retrieves a list of available persona templates.

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `page` | integer | Page number (default: 1) |
| `limit` | integer | Items per page (default: 10, max: 100) |
| `category` | string | Filter by template category |

**Response:**

```json
{
  "templates": [
    {
      "templateId": "template_12345",
      "name": "Customer Support Agent",
      "description": "A helpful customer support agent template",
      "placeholders": {
        "company_name": {
          "description": "The name of your company",
          "defaultValue": "ACME Inc."
        },
        "product_name": {
          "description": "The name of your product",
          "defaultValue": "Widget Pro"
        }
      },
      "createdAt": "2023-06-15T14:30:00.000Z"
    },
    // More templates...
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalCount": 25,
    "totalPages": 3
  }
}
```

#### Get Template

```
GET /templates/:id
```

Retrieves a specific template by ID.

**Response:**

```json
{
  "template": {
    "templateId": "template_12345",
    "name": "Customer Support Agent",
    "description": "A helpful customer support agent template",
    "system": "You are a customer support agent for {{company_name}}...",
    "bio": ["Customer support specialist for {{product_name}}", "..."],
    "style": {
      "all": ["Be helpful and courteous", "..."],
      "chat": ["Address the customer by name when possible", "..."],
      "post": ["Use formal language for announcements", "..."]
    },
    "placeholders": {
      "company_name": {
        "description": "The name of your company",
        "defaultValue": "ACME Inc."
      },
      "product_name": {
        "description": "The name of your product",
        "defaultValue": "Widget Pro"
      }
    },
    "createdAt": "2023-06-15T14:30:00.000Z"
  }
}
```

#### Create Persona from Template

```
POST /templates/:id/create
```

Creates a new persona from a template.

**Request Body:**

```json
{
  "placeholders": {
    "company_name": "TechCorp",
    "product_name": "CloudService"
  }
}
```

**Response:**

```json
{
  "persona": {
    "id": "persona_67890",
    "name": "Customer Support Agent",
    "system": "You are a customer support agent for TechCorp...",
    "bio": ["Customer support specialist for CloudService", "..."],
    "style": {
      "all": ["Be helpful and courteous", "..."],
      "chat": ["Address the customer by name when possible", "..."],
      "post": ["Use formal language for announcements", "..."]
    },
    "createdAt": "2023-06-16T10:00:00.000Z",
    "updatedAt": "2023-06-16T10:00:00.000Z"
  }
}
```

## Error Handling

API errors are returned with appropriate HTTP status codes and a JSON response body:

```json
{
  "error": {
    "code": "invalid_request",
    "message": "The request was invalid",
    "details": [
      {
        "field": "name",
        "message": "Name is required"
      }
    ]
  }
}
```

Common error codes:

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| 400 | `invalid_request` | The request was malformed or contained invalid parameters |
| 401 | `unauthorized` | Authentication is required or failed |
| 403 | `forbidden` | The authenticated user does not have permission |
| 404 | `not_found` | The requested resource was not found |
| 409 | `conflict` | The request conflicts with the current state |
| 429 | `rate_limit_exceeded` | The rate limit has been exceeded |
| 500 | `server_error` | An unexpected server error occurred |

## Webhooks

Webhooks allow you to receive notifications when certain events occur in the system.

### Configuring Webhooks

Webhooks can be configured in the developer dashboard. For each webhook, you need to specify:

1. The endpoint URL where events will be sent
2. The events you want to subscribe to
3. An optional secret for verifying webhook signatures

### Webhook Events

| Event | Description |
|-------|-------------|
| `persona.created` | A new persona was created |
| `persona.updated` | A persona was updated |
| `persona.deleted` | A persona was deleted |

### Webhook Payload

Webhook payloads are sent as HTTP POST requests with a JSON body:

```json
{
  "event": "persona.created",
  "timestamp": "2023-06-16T10:00:00.000Z",
  "data": {
    "persona": {
      "id": "persona_12345",
      "name": "Technical Expert",
      "createdAt": "2023-06-16T10:00:00.000Z"
    }
  }
}
```

### Webhook Signatures

To verify that a webhook came from xsche, we include a signature in the `X-Xsche-Signature` header. The signature is a HMAC SHA-256 hash of the request body, using your webhook secret as the key.

## Pagination

List endpoints support pagination using the following query parameters:

| Parameter | Type | Description |
|-----------|------|-------------|
| `page` | integer | Page number (default: 1) |
| `limit` | integer | Items per page (default: 10, max: 100) |

Pagination information is included in the response:

```json
{
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalCount": 25,
    "totalPages": 3
  }
}
```

## Versioning

The API is versioned using the URL path (e.g., `/v1/personas`). When breaking changes are introduced, a new version will be released.

## SDKs

Official SDKs are available for the following languages:

- JavaScript/TypeScript: [xsche-js](https://github.com/xsche/xsche-js)
- Python: [xsche-python](https://github.com/xsche/xsche-python)
- Ruby: [xsche-ruby](https://github.com/xsche/xsche-ruby)
- Go: [xsche-go](https://github.com/xsche/xsche-go)

## Support

For API support, <NAME_EMAIL> or visit the [Developer Forum](https://forum.xsche.com/developers).

