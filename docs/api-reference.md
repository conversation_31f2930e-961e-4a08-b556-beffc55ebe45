# Persona Management System API Reference

This document provides detailed information about the API endpoints available in the Persona Management System.

## Authentication

All API endpoints require authentication. The system uses NextAuth.js for authentication, and all requests must include a valid session cookie or bearer token.

## Base URL

All API endpoints are relative to the base URL of your application.

## Response Format

All API responses follow a standard format:

```json
{
  "success": true,
  "data": { ... },
  "error": null
}
```

Or in case of an error:

```json
{
  "success": false,
  "data": null,
  "error": "Error message"
}
```

## Endpoints

### Get All Personas

Retrieves all personas for the current user.

**URL**: `/api/bots/personas`

**Method**: `GET`

**Query Parameters**:
- `active` (optional): If set to `true`, returns only the active persona

**Response**:
```json
{
  "personas": [
    {
      "id": "clj1a2b3c4d5e6f7g8h9i0",
      "userId": "user123",
      "name": "Professional Writer",
      "description": "A professional content writer",
      "system": "You are a professional content writer...",
      "bio": ["Experienced content writer", "SEO specialist"],
      "topics": ["marketing", "SEO", "content strategy"],
      "adjectives": ["professional", "clear", "concise"],
      "isActive": true,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-02T00:00:00.000Z"
    },
    // More personas...
  ]
}
```

### Create Persona

Creates a new persona.

**URL**: `/api/bots/personas`

**Method**: `POST`

**Request Body**:
```json
{
  "name": "Technical Expert",
  "system": "You are a technical expert...",
  "bio": ["Software engineer with 10+ years experience", "Open source contributor"],
  "topics": ["programming", "software architecture", "DevOps"],
  "adjectives": ["technical", "precise", "detailed"]
}
```

**Response**:
```json
{
  "persona": {
    "id": "clj1a2b3c4d5e6f7g8h9i0",
    "userId": "user123",
    "name": "Technical Expert",
    "system": "You are a technical expert...",
    "bio": ["Software engineer with 10+ years experience", "Open source contributor"],
    "topics": ["programming", "software architecture", "DevOps"],
    "adjectives": ["technical", "precise", "detailed"],
    "isActive": false,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Get Persona

Retrieves a specific persona by ID.

**URL**: `/api/bots/personas/[id]`

**Method**: `GET`

**URL Parameters**:
- `id`: The ID of the persona to retrieve

**Response**:
```json
{
  "persona": {
    "id": "clj1a2b3c4d5e6f7g8h9i0",
    "userId": "user123",
    "name": "Technical Expert",
    "system": "You are a technical expert...",
    "bio": ["Software engineer with 10+ years experience", "Open source contributor"],
    "topics": ["programming", "software architecture", "DevOps"],
    "adjectives": ["technical", "precise", "detailed"],
    "isActive": false,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Update Persona

Updates an existing persona.

**URL**: `/api/bots/personas/[id]`

**Method**: `PUT`

**URL Parameters**:
- `id`: The ID of the persona to update

**Request Body**:
```json
{
  "name": "Technical Expert 2.0",
  "system": "You are a senior technical expert...",
  "topics": ["programming", "software architecture", "DevOps", "cloud computing"]
}
```

**Response**:
```json
{
  "persona": {
    "id": "clj1a2b3c4d5e6f7g8h9i0",
    "userId": "user123",
    "name": "Technical Expert 2.0",
    "system": "You are a senior technical expert...",
    "bio": ["Software engineer with 10+ years experience", "Open source contributor"],
    "topics": ["programming", "software architecture", "DevOps", "cloud computing"],
    "adjectives": ["technical", "precise", "detailed"],
    "isActive": false,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

### Delete Persona

Deletes a persona.

**URL**: `/api/bots/personas/[id]`

**Method**: `DELETE`

**URL Parameters**:
- `id`: The ID of the persona to delete

**Response**:
```json
{
  "success": true
}
```

### Set Active Persona

Sets a persona as the active persona for the current user.

**URL**: `/api/bots/personas/[id]/active`

**Method**: `POST`

**URL Parameters**:
- `id`: The ID of the persona to set as active

**Response**:
```json
{
  "persona": {
    "id": "clj1a2b3c4d5e6f7g8h9i0",
    "userId": "user123",
    "name": "Technical Expert",
    "system": "You are a technical expert...",
    "bio": ["Software engineer with 10+ years experience", "Open source contributor"],
    "topics": ["programming", "software architecture", "DevOps"],
    "adjectives": ["technical", "precise", "detailed"],
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
}
```

### Import Persona

Imports a persona from JSON data.

**URL**: `/api/bots/personas/import`

**Method**: `POST`

**Request Body**:
```json
{
  "jsonData": "{\"name\":\"Creative Writer\",\"system\":\"You are a creative writer...\",\"bio\":[\"Fiction author\",\"Poet\"],\"topics\":[\"creative writing\",\"storytelling\",\"poetry\"],\"adjectives\":[\"creative\",\"imaginative\",\"expressive\"]}"
}
```

**Response**:
```json
{
  "success": true,
  "persona": {
    "id": "clj1a2b3c4d5e6f7g8h9i0",
    "userId": "user123",
    "name": "Creative Writer",
    "system": "You are a creative writer...",
    "bio": ["Fiction author", "Poet"],
    "topics": ["creative writing", "storytelling", "poetry"],
    "adjectives": ["creative", "imaginative", "expressive"],
    "isActive": false,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Export Persona

Exports a persona to JSON format.

**URL**: `/api/bots/personas/[id]/export`

**Method**: `GET`

**URL Parameters**:
- `id`: The ID of the persona to export

**Query Parameters**:
- `metadata` (optional): If set to `true`, includes metadata like creation date

**Response**:
```json
{
  "data": "{\"name\":\"Technical Expert\",\"system\":\"You are a technical expert...\",\"bio\":[\"Software engineer with 10+ years experience\",\"Open source contributor\"],\"topics\":[\"programming\",\"software architecture\",\"DevOps\"],\"adjectives\":[\"technical\",\"precise\",\"detailed\"]}"
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - The request was malformed or contained invalid parameters |
| 401 | Unauthorized - Authentication is required |
| 403 | Forbidden - The user does not have permission to access the resource |
| 404 | Not Found - The requested resource was not found |
| 500 | Internal Server Error - An unexpected error occurred on the server |

## Rate Limiting

API requests are subject to rate limiting to prevent abuse. The current limits are:

- 100 requests per minute per user
- 1000 requests per hour per user

If you exceed these limits, you will receive a 429 Too Many Requests response.

## Pagination

For endpoints that return collections of resources, pagination is supported using the following query parameters:

- `page`: The page number (starting from 1)
- `limit`: The number of items per page (default: 10, max: 100)

Example:
```
GET /api/bots/personas?page=2&limit=20
```

## Filtering

Some endpoints support filtering using query parameters:

- `/api/bots/personas?search=writer`: Search personas by name or description
- `/api/bots/personas?topic=programming`: Filter personas by topic

## Sorting

Some endpoints support sorting using the `sort` query parameter:

- `/api/bots/personas?sort=name`: Sort personas by name (ascending)
- `/api/bots/personas?sort=-createdAt`: Sort personas by creation date (descending)

## Versioning

The current API version is v1. The version is not included in the URL path but may be included in future releases.

## Webhooks

The API supports webhooks for real-time notifications of persona changes. To register a webhook:

1. Create a webhook endpoint on your server
2. Register the webhook URL through the API
3. Receive notifications when personas are created, updated, or deleted

## SDK

A JavaScript SDK is available for easier integration with the Persona Management System API:

```javascript
import { PersonaClient } from '@xsche/persona-sdk';

const client = new PersonaClient({
  apiKey: 'your-api-key'
});

// Get all personas
const personas = await client.getPersonas();

// Create a new persona
const newPersona = await client.createPersona({
  name: 'Customer Support',
  system: 'You are a helpful customer support agent...',
  bio: ['Experienced in customer service'],
  topics: ['customer support', 'problem solving']
});
```

## Support

If you encounter any issues with the API, please contact <NAME_EMAIL> or open an issue on GitHub.

