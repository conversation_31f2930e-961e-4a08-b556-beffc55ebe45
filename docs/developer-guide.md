# Persona Management System Developer Guide

## Architecture Overview

The Persona Management System is built using a modern web architecture with the following components:

- **Frontend**: Next.js with React and TypeScript
- **UI Components**: shadcn/ui component library
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **API**: RESTful API endpoints using Next.js API routes

## Database Schema

The system uses the following database models:

### BotPersona

The main model for storing persona data:

```typescript
model BotPersona {
  id          String   @id @default(cuid())
  userId      String
  name        String
  description String?
  system      String?
  bio         Json?    // String array
  lore        Json?    // String array
  messageExamples Json? // Array of message examples
  postExamples Json?   // String array
  adjectives  Json?    // String array
  topics      Json?    // String array
  style       Json?    // Object with style guidelines
  version     Int      @default(1)
  isActive    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([isActive])
}
```

## API Endpoints

The system provides the following API endpoints:

### Persona Management

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/bots/personas` | GET | Get all personas for the current user |
| `/api/bots/personas` | POST | Create a new persona |
| `/api/bots/personas?active=true` | GET | Get the active persona |
| `/api/bots/personas/[id]` | GET | Get a specific persona |
| `/api/bots/personas/[id]` | PUT | Update a specific persona |
| `/api/bots/personas/[id]` | DELETE | Delete a specific persona |
| `/api/bots/personas/[id]/active` | POST | Set a persona as active |
| `/api/bots/personas/import` | POST | Import a persona from JSON |
| `/api/bots/personas/[id]/export` | GET | Export a persona to JSON |

## Component Structure

The system is organized into the following component structure:

### Core Components

- `PersonaEditor`: Main editor component for creating and editing personas
- `PersonaFormField`: Reusable form field component for different input types
- `PersonaGrid`: Grid display of personas with search and filtering
- `PersonaCard`: Card component for displaying persona previews
- `PersonaSelector`: Component for selecting personas
- `PersonaHeaderSelector`: Header component for quick persona switching
- `PersonaImportExport`: Component for importing and exporting personas
- `PersonaFileUpload`: File upload component for importing personas
- `PersonaSwitcher`: Dropdown component for switching personas
- `PersonaQuickSwitcher`: Dashboard integration component
- `PersonaSelectorPanel`: Dashboard sidebar component

### Directory Structure

```
/components
  /bots
    persona-card.tsx
    persona-editor.tsx
    persona-file-upload.tsx
    persona-form-field.tsx
    persona-grid.tsx
    persona-header-selector.tsx
    persona-import-export.tsx
    persona-selector.tsx
    persona-switcher.tsx
    persona-theme.tsx
/app
  /api
    /bots
      /personas
        /[id]
          /active
            route.ts
          /export
            route.ts
          route.ts
        /import
          route.ts
        route.ts
  /bots
    /personas
      page.tsx
  /dashboard
    /components
      persona-quick-switcher.tsx
      persona-selector-panel.tsx
/lib
  /bots
    persona-service.ts
    types.ts
```

## Core Services

### PersonaService

The `PersonaService` class handles all persona-related operations:

```typescript
class PersonaService {
  constructor(private db: PrismaClient) {}

  // Get all personas for a user
  async getPersonas(userId: string): Promise<BotPersona[]> { ... }

  // Get a specific persona
  async getPersona(id: string): Promise<BotPersona | null> { ... }

  // Create a new persona
  async createPersona(userId: string, data: EnhancedPersonaTemplate): Promise<BotPersona> { ... }

  // Update an existing persona
  async updatePersona(id: string, data: Partial<EnhancedPersonaTemplate>): Promise<BotPersona> { ... }

  // Delete a persona
  async deletePersona(id: string): Promise<boolean> { ... }

  // Set a persona as active
  async setActivePersona(userId: string, personaId: string): Promise<BotPersona> { ... }

  // Get the active persona for a user
  async getActivePersona(userId: string): Promise<BotPersona | null> { ... }

  // Import a persona from JSON
  async importPersona(userId: string, jsonData: string): Promise<PersonaImportResult> { ... }

  // Export a persona to JSON
  async exportPersona(id: string, options?: PersonaExportOptions): Promise<PersonaExportResult> { ... }
}
```

## Type Definitions

The system uses the following TypeScript types:

```typescript
// Basic persona template (legacy format)
interface PersonaTemplate {
  name: string;
  description?: string;
  tone: string;
  writingStyle: string;
  vocabulary: string;
  traits: string[];
  knowledge: string[];
  rules: string[];
  contextLength: number;
}

// Enhanced persona template with additional fields
interface EnhancedPersonaTemplate {
  name: string;
  system: string;
  bio: string[];
  lore?: string[];
  messageExamples?: any[];
  postExamples?: string[];
  adjectives?: string[];
  topics?: string[];
  style?: {
    all?: string[];
    chat?: string[];
    post?: string[];
  };
  version?: number;
}

// Result of persona import operation
interface PersonaImportResult {
  success: boolean;
  error?: string;
  personaId?: string;
}

// Options for persona export
interface PersonaExportOptions {
  includeMetadata?: boolean;
}

// Result of persona export operation
interface PersonaExportResult {
  success: boolean;
  error?: string;
  data?: string;
}
```

## Implementation Guidelines

### Adding New Fields to Personas

To add new fields to the persona format:

1. Update the `BotPersona` model in the Prisma schema
2. Create and run a migration to update the database
3. Update the `EnhancedPersonaTemplate` interface in `types.ts`
4. Update the `PersonaEditor` component to include the new fields
5. Update the import/export logic to handle the new fields

### Creating New UI Components

When creating new UI components:

1. Follow the existing component structure and naming conventions
2. Use shadcn/ui components for consistency
3. Implement proper error handling and loading states
4. Add appropriate animations and transitions
5. Ensure accessibility compliance

### Adding New API Endpoints

To add new API endpoints:

1. Create a new route file in the appropriate directory
2. Implement proper authentication and validation
3. Use the `PersonaService` for database operations
4. Return standardized response formats
5. Update the developer documentation

## Testing

### Unit Testing

Unit tests should be written for:

- Service methods
- API endpoints
- Utility functions

### Integration Testing

Integration tests should cover:

- End-to-end persona creation, editing, and deletion
- Import/export functionality
- Active persona selection

### UI Testing

UI tests should verify:

- Component rendering
- User interactions
- Error handling
- Accessibility compliance

## Performance Considerations

- Use pagination for large collections of personas
- Implement proper caching for frequently accessed data
- Optimize database queries with appropriate indexes
- Lazy load components when appropriate
- Use optimized animations that don't impact performance

## Security Guidelines

- Always validate user input on both client and server
- Implement proper authentication and authorization
- Sanitize data before storing in the database
- Use HTTPS for all API requests
- Follow the principle of least privilege

## Extending the System

### Adding New Persona Features

To add new features to the persona system:

1. Define the requirements and design the feature
2. Update the database schema if necessary
3. Implement the backend logic in the `PersonaService`
4. Create or update API endpoints
5. Implement the UI components
6. Update the documentation

### Integration with Other Systems

The persona system can be integrated with:

- Content generation systems
- Social media management tools
- Customer support platforms
- Analytics systems

## Troubleshooting

### Common Issues

- **Database Connection Errors**: Check the database connection string and credentials
- **API Errors**: Verify the request format and authentication
- **UI Rendering Issues**: Check for missing props or state updates
- **Import/Export Errors**: Validate the JSON format and required fields

### Debugging Tools

- Use browser developer tools for frontend debugging
- Check server logs for backend errors
- Use Prisma Studio for database inspection
- Implement logging throughout the application

## Deployment

The system can be deployed using:

- Vercel for Next.js applications
- AWS, GCP, or Azure for cloud hosting
- Docker containers for containerized deployment
- CI/CD pipelines for automated deployment

## Contributing

When contributing to the persona management system:

1. Follow the existing code style and conventions
2. Write comprehensive tests for new features
3. Update documentation to reflect changes
4. Submit pull requests with clear descriptions
5. Address code review feedback promptly

