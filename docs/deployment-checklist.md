# Deployment Checklist

## Pre-Deployment Checklist

### ✅ Environment Configuration

- [ ] **NEXT_PUBLIC_BASE_URL** is set to your production domain
- [ ] **NODE_ENV** is set to `production`
- [ ] **PORT** is configured (default: 3030)
- [ ] **NEXTAUTH_URL** matches NEXT_PUBLIC_BASE_URL
- [ ] All OAuth credentials are production values (not development)

### ✅ OAuth Provider Configuration

#### Google OAuth
- [ ] Google Cloud Console configured with production callback URL
- [ ] Callback URL: `https://your-domain.com/api/auth/google/callback`
- [ ] Production client ID and secret in environment variables

#### Twitter OAuth
- [ ] Twitter Developer Portal configured with production callback URL
- [ ] Callback URL: `https://your-domain.com/api/auth/twitter/callback`
- [ ] Production API key and secret in environment variables

### ✅ Database Configuration

- [ ] Production database URL configured
- [ ] Database migrations applied
- [ ] Database connection tested

### ✅ Security Configuration

- [ ] JWT_SECRET is a strong, unique value
- [ ] SESSION_SECRET is a strong, unique value
- [ ] NEXTAUTH_SECRET is configured
- [ ] ENCRYPTION_KEY is 32 characters for AES-256
- [ ] All API keys are production values

## Deployment Commands

### Standalone Deployment (Recommended - Default)
```bash
# Build the application with standalone output
npm run build

# Start standalone server (default start command)
npm run start
```

### Alternative: Traditional Next.js Server
```bash
# Build the application
npm run build

# Start with next start (only if standalone is disabled)
npm run start:next
```

### Development
```bash
# Start development server
npm run dev
# or
npm run start:dev
```

## Post-Deployment Verification

### ✅ Application Health

- [ ] Application starts without errors
- [ ] Console shows correct configuration:
  ```
  🔧 Config loaded: {
    NODE_ENV: 'production',
    baseUrl: 'https://your-domain.com',
    googleCallbackUrl: 'https://your-domain.com/api/auth/google/callback',
    twitterCallbackUrl: 'https://your-domain.com/api/auth/twitter/callback',
    usingEnvVar: 'NEXT_PUBLIC_BASE_URL'
  }
  ```
- [ ] No localhost URLs in production logs
- [ ] Application accessible on production domain

### ✅ OAuth Testing

- [ ] Google OAuth login works
- [ ] Twitter OAuth login works
- [ ] Callbacks redirect to correct URLs
- [ ] User sessions persist correctly

### ✅ Database Connectivity

- [ ] Database queries execute successfully
- [ ] User data persists correctly
- [ ] No connection errors in logs

## Troubleshooting

### Common Issues

1. **OAuth callbacks fail**
   - Check NEXT_PUBLIC_BASE_URL matches OAuth provider configuration
   - Verify callback URLs in provider consoles

2. **Localhost URLs in production**
   - Ensure NODE_ENV=production
   - Verify NEXT_PUBLIC_BASE_URL is set correctly

3. **Application not accessible externally**
   - Check --hostname 0.0.0.0 in start script
   - Verify nginx/proxy configuration

4. **Environment variable warnings**
   - Check console for configuration warnings
   - Ensure all required variables are set

### Debug Commands

```bash
# Check environment variables
env | grep -E "(NODE_ENV|NEXT_PUBLIC_BASE_URL|PORT)"

# Test OAuth callback URLs
curl -I https://your-domain.com/api/auth/google/callback
curl -I https://your-domain.com/api/auth/twitter/callback

# Check application health
curl -I https://your-domain.com/
```

## Rollback Plan

If deployment fails:

1. **Immediate rollback**
   ```bash
   # Stop current deployment
   pkill -f "node .next/standalone/server.js"
   
   # Restart previous version
   # (restore previous build or restart previous container)
   ```

2. **Environment restoration**
   - Restore previous .env file
   - Verify OAuth provider configurations
   - Test critical functionality

3. **Database rollback** (if needed)
   - Restore database backup
   - Verify data integrity
