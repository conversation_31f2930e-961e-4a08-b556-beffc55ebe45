# 🔗 URL Configuration Guide for xsche

## Overview

This document explains the standardized URL configuration for the xsche AI-powered tweet scheduler. The configuration has been simplified to use a single domain approach for both development and production.

## 🏗️ Architecture

### Development Environment
- **Application**: `http://localhost:3000`
- **OAuth Callbacks**: `http://localhost:3000/api/auth/[provider]/callback`

### Production Environment  
- **Application**: `https://tasker.violetmethods.com`
- **OAuth Callbacks**: `https://tasker.violetmethods.com/api/auth/[provider]/callback`

## 📋 Environment Variables

### Core Configuration

| Variable | Development | Production | Purpose |
|----------|-------------|------------|---------|
| `NEXT_PUBLIC_BASE_URL` | `http://localhost:3000` | `https://tasker.violetmethods.com` | Base URL for OAuth callbacks and redirects |
| `NODE_ENV` | `development` | `production` | Environment mode |
| `PORT` | `3000` | `3000` | Application port |
| `NEXTAUTH_URL` | `http://localhost:3000` | `https://tasker.violetmethods.com` | NextAuth base URL |

### OAuth Callback URLs

The OAuth callback URLs are automatically constructed using the `NEXT_PUBLIC_BASE_URL`:

- **Google OAuth**: `{NEXT_PUBLIC_BASE_URL}/api/auth/google/callback`
- **Twitter OAuth**: `{NEXT_PUBLIC_BASE_URL}/api/auth/twitter/callback`

## 🔧 OAuth Provider Configuration

### Google OAuth Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services > Credentials
3. Update your OAuth 2.0 Client ID with these authorized redirect URIs:
   - Development: `http://localhost:3000/api/auth/google/callback`
   - Production: `https://tasker.violetmethods.com/api/auth/google/callback`

### Twitter Developer Console
1. Go to [Twitter Developer Portal](https://developer.twitter.com/)
2. Navigate to your app settings
3. Update the callback URL:
   - Development: `http://localhost:3000/api/auth/twitter/callback`
   - Production: `https://tasker.violetmethods.com/api/auth/twitter/callback`

## 🚀 Deployment

### For Development
```bash
# Use the current .env file (already configured for localhost:3000)
bun dev

# Note: If port 3000 is in use, Next.js will automatically use 3001
# Update NEXT_PUBLIC_BASE_URL accordingly if needed
```

### For Production
```bash
# Copy the production template
cp .env.production.example .env

# Update the following variables in .env:
# - NEXT_PUBLIC_BASE_URL=https://tasker.violetmethods.com
# - All OAuth credentials with production values
# - Database URLs
# - API keys

# Build and start
bun build
bun start
```

## 🔍 Verification

### Test OAuth Flows

1. **Development Testing**:
   ```bash
   # Start the app
   bun dev
   
   # Test OAuth URLs:
   # Google: http://localhost:3000/api/auth/google
   # Twitter: http://localhost:3000/api/auth/twitter
   ```

2. **Production Testing**:
   ```bash
   # Test OAuth URLs:
   # Google: https://tasker.violetmethods.com/api/auth/google  
   # Twitter: https://tasker.violetmethods.com/api/auth/twitter
   ```

## 🛠️ Troubleshooting

### Common Issues

1. **OAuth Redirect Mismatch**
   - Ensure callback URLs in provider consoles match your `NEXT_PUBLIC_BASE_URL`
   - Check for trailing slashes or protocol mismatches

2. **Port Conflicts**
   - Default port is now 3000 (standard Next.js)
   - Update `PORT` environment variable if needed

3. **Environment Variable Missing**
   - Ensure `NEXT_PUBLIC_BASE_URL` is set in your `.env` file
   - This variable is critical for OAuth callback construction

### Debug Commands

```bash
# Check current configuration
echo $NEXT_PUBLIC_BASE_URL

# Test OAuth URL configuration
node scripts/test-oauth-urls.js

# Verify OAuth callback URLs are constructed correctly
# Check lib/config.ts for the actual URLs being used
```

## 📝 Migration Notes

### Changes Made
- Standardized port to 3000 (from mixed 3000/4041)
- Added `NEXT_PUBLIC_BASE_URL` as the primary URL configuration
- Fixed Twitter callback URL path (was `/api/v1/user/path/auth/twitter/callback`)
- Simplified to single domain approach (no API subdomain complexity)
- Created production environment template

### Legacy Variables
These variables are kept for compatibility but are no longer primary:
- `BASE_URL`
- `MAIN_URL` 
- `NEXT_PUBLIC_DOMAIN`
- `NEXT_PUBLIC_API_URL`

## 🎯 Benefits

1. **Simplified Configuration**: Single `NEXT_PUBLIC_BASE_URL` drives all OAuth URLs
2. **Environment Consistency**: Same structure for dev and prod
3. **Easy Deployment**: Clear production template
4. **Standard Ports**: Uses Next.js default port (3000)
5. **Single Domain**: No subdomain complexity for OAuth callbacks
