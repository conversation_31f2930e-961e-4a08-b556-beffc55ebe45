# Authentication System Documentation

## Overview

The xsche application uses a custom JWT-based authentication system optimized for serverless deployment. This document provides a comprehensive overview of the authentication system, including the authentication flows, API routes, middleware, and integration with third-party providers.

## Authentication Methods

The application supports three authentication methods:

1. **Email/Password**: Traditional username/password authentication
2. **Google OAuth**: Sign in with Google
3. **Twitter OAuth**: Sign in with Twitter

## JWT-Based Authentication

The application uses JSON Web Tokens (JWTs) for authentication. JWTs are stateless, making them ideal for serverless environments. The tokens are stored in HTTP-only cookies for security.

### Key Components

- **JWT Secret**: A secret key used to sign and verify JWTs, stored in the `JWT_SECRET` environment variable
- **Token Expiration**: JWTs expire after a configurable period (default: 7 days)
- **HTTP-Only Cookies**: JWTs are stored in HTTP-only cookies to prevent client-side JavaScript access
- **Secure Flag**: In production, cookies are set with the `secure` flag to ensure they're only sent over HTTPS

## Authentication Flow

### Email/Password Authentication

1. User submits email and password to `/api/auth/login`
2. Server validates credentials against the database
3. If valid, a JWT is generated and set as an HTTP-only cookie
4. User is redirected to the dashboard or requested page

### OAuth Authentication (Google/Twitter)

1. User clicks "Sign in with Google/Twitter"
2. User is redirected to the OAuth provider's authorization page
3. After authorization, the provider redirects back to our callback URL
4. Server verifies the OAuth response and retrieves user information
5. If the user exists, they are logged in; if not, a new account is created
6. A JWT is generated and set as an HTTP-only cookie
7. User is redirected to the dashboard or requested page

## API Routes

### Authentication Routes

- **POST `/api/auth/login`**: Email/password login
- **POST `/api/auth/register`**: User registration with email/password
- **GET `/api/auth/logout`**: Logout (clears the JWT cookie)
- **GET `/api/auth/me`**: Returns the current authenticated user's information
- **GET `/api/auth/google`**: Initiates Google OAuth flow
- **GET `/api/auth/google/callback`**: Handles Google OAuth callback
- **GET `/api/auth/twitter`**: Initiates Twitter OAuth flow
- **GET `/api/auth/twitter/callback`**: Handles Twitter OAuth callback

## Middleware

The application uses a custom middleware (`middleware.ts`) to protect routes that require authentication. The middleware:

1. Extracts the JWT from the request cookies
2. Verifies the JWT's signature and expiration
3. If valid, attaches the user information to the request object
4. If invalid or missing, redirects unauthenticated users to the login page

## Twitter Integration

There are two distinct Twitter integrations in the application:

1. **Authentication with Twitter**: Allows users to sign up or log in using their Twitter account
   - Implemented in `/api/auth/twitter/` and `/api/auth/twitter/callback/`
   - Uses Twitter API v1.0a via the twitter-api-v2 library
   - Stores the Twitter user ID in the User model

2. **Twitter Account Integration for Features**: Allows users to connect Twitter accounts to use the scheduler and other features
   - Implemented in `/api/twitter/connect/` and `/api/twitter/connect/callback/`
   - Available regardless of how the user initially authenticated (Google, email, etc.)
   - Stores Twitter access tokens in the TwitterAccount model
   - Managed through the Integrations page in the dashboard

## Database Models

### User Model

The User model stores basic user information and authentication details:

```prisma
model User {
  id                    String           @id @default(cuid())
  email                 String           @unique
  emailVerified         Boolean          @default(false)
  hashedPassword        String?
  name                  String?
  googleId              String?          @unique
  twitterId             String?          @unique
  profilePicture        String?
  // ... other fields ...
  twitterAccounts       TwitterAccount[]
  // ... relationships ...
}
```

### TwitterAccount Model

The TwitterAccount model stores Twitter account details for posting tweets:

```prisma
model TwitterAccount {
  id              String           @id @default(cuid())
  userId          String
  twitterUserId   String          @map("twitter_user_id_str")
  screenName      String
  name            String
  profilePicture  String?
  accessToken     Bytes           // Encrypted
  refreshToken    Bytes?          // Encrypted
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  scheduledTweets ScheduledTweet[]
  // ... relationships ...
}
```

## Security Considerations

1. **Password Hashing**: Passwords are hashed using bcrypt before storage
2. **HTTP-Only Cookies**: JWTs are stored in HTTP-only cookies to prevent client-side access
3. **CSRF Protection**: The application implements CSRF protection for all state-changing operations
4. **Token Encryption**: OAuth tokens are encrypted before storage in the database
5. **Rate Limiting**: Authentication endpoints implement rate limiting to prevent brute force attacks

## Environment Variables

The authentication system requires the following environment variables:

```
JWT_SECRET=your-jwt-secret-key
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
NEXT_PUBLIC_BASE_URL=https://your-app-url.com
```

## Best Practices

1. **Keep JWT Secret Secure**: The JWT secret should be a strong, random string and kept secure
2. **Rotate OAuth Credentials**: Regularly rotate OAuth client secrets for Google and Twitter
3. **Monitor Failed Login Attempts**: Implement monitoring for suspicious login patterns
4. **Regular Security Audits**: Conduct regular security audits of the authentication system
5. **Stay Updated**: Keep dependencies up-to-date to address security vulnerabilities

## Troubleshooting

### Common Issues

1. **JWT Verification Failures**: Check that the `JWT_SECRET` environment variable is consistent across deployments
2. **OAuth Callback Errors**: Ensure callback URLs are correctly configured in the OAuth provider dashboards
3. **Cookie Issues**: In development, ensure cookies are properly configured for localhost
4. **CORS Issues**: For API routes, ensure CORS is properly configured if accessing from different origins

## Future Improvements

1. **Implement Refresh Tokens**: Add refresh token functionality for longer sessions
2. **Add Multi-Factor Authentication**: Enhance security with MFA options
3. **Implement OAuth 2.0 PKCE**: Use PKCE for OAuth flows to enhance security
4. **Add More OAuth Providers**: Expand authentication options with additional providers
5. **Implement Account Linking**: Allow users to link multiple authentication methods to a single account

