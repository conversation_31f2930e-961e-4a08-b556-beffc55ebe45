# Environment configuration for Tasker
#
# Copy this file to .env and fill in your actual values
# Never commit .env files with real credentials to version control

# Application Configuration
NODE_ENV=development
PORT=3030

# Base URL Configuration (Primary - used for OAuth callbacks)
# Development: http://localhost:3030
# Production: https://your-domain.com
NEXT_PUBLIC_BASE_URL=

# NextAuth URL (for compatibility)
NEXTAUTH_URL=http://localhost:3030
NEXTAUTH_SECRET=your-nextauth-secret-key
# Authentication Configuration
JWT_SECRET=your-jwt-secret-here
SESSION_SECRET=your-session-secret-here

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Twitter OAuth
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret

# Admin Configuration (for development)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin12345

# Database Configuration
DATABASE_URL=postgresql://user:password@host:port/database
DIRECT_URL=postgresql://user:password@host:port/database

# API Keys

# Uploadthing Configuration
UPLOADTHING_SECRET=your-uploadthing-secret
UPLOADTHING_APP_ID=your-uploadthing-app-id
UPLOADTHING_TOKEN=your-uploadthing-token

# Cron Jobs Configuration
ENABLE_CRON=true
CRON_SECRET=your-cron-secret

# Encryption Key (32 characters for AES-256)
ENCRYPTION_KEY=your-32-character-encryption-key
