-- AlterTable: Add enhanced persona fields to BotPersona table
ALTER TABLE "BotPersona" 
ADD COLUMN IF NOT EXISTS "system" TEXT,
ADD COLUMN IF NOT EXISTS "bio" JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS "lore" JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS "messageExamples" JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS "postExamples" JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS "adjectives" JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS "topics" JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS "style" JSONB DEFAULT '{"all":[],"chat":[],"post":[]}',
ADD COLUMN IF NOT EXISTS "version" INTEGER DEFAULT 1;

-- Create index on name for faster searches
CREATE INDEX IF NOT EXISTS "BotPersona_name_idx" ON "BotPersona"("name");

-- Create UserPreference table for storing persona preferences
CREATE TABLE IF NOT EXISTS "UserPreference" (
  "id" TEXT NOT NULL,
  "userId" TEXT NOT NULL,
  "key" TEXT NOT NULL,
  "value" JSONB NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  
  CONSTRAINT "UserPreference_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on userId and key
CREATE UNIQUE INDEX IF NOT EXISTS "UserPreference_userId_key_key" ON "UserPreference"("userId", "key");

-- Add foreign key constraint
ALTER TABLE "UserPreference" 
ADD CONSTRAINT "UserPreference_userId_fkey" 
FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

