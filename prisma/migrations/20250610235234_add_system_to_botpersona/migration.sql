/*
  Warnings:

  - You are about to drop the `UserPreference` table. If the table is not empty, all the data it contains will be lost.
  - Made the column `version` on table `BotPersona` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "UserPreference" DROP CONSTRAINT "UserPreference_userId_fkey";

-- DropIndex
DROP INDEX "BotPersona_name_idx";

-- AlterTable
ALTER TABLE "BotPersona" ADD COLUMN     "isActive" BOOLEAN NOT NULL DEFAULT false,
ALTER COLUMN "bio" DROP DEFAULT,
ALTER COLUMN "lore" DROP DEFAULT,
ALTER COLUMN "messageExamples" DROP DEFAULT,
ALTER COLUMN "postExamples" DROP DEFAULT,
ALTER COLUMN "adjectives" DROP DEFAULT,
ALTER COLUMN "topics" DROP DEFAULT,
ALTER COLUMN "style" DROP DEFAULT,
ALTER COLUMN "version" SET NOT NULL;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "emailVerified" BOOLEAN NOT NULL DEFAULT false;

-- DropTable
DROP TABLE "UserPreference";

-- CreateIndex
CREATE INDEX "BotPersona_isActive_idx" ON "BotPersona"("isActive");
