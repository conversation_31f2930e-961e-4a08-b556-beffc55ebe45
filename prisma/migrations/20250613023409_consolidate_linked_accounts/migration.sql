/*
  Warnings:

  - You are about to drop the `TwitterAccount` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropFore<PERSON>Key
ALTER TABLE "ScheduledTweet" DROP CONSTRAINT "ScheduledTweet_twitterAccountId_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "TwitterAccount" DROP CONSTRAINT "TwitterAccount_userId_fkey";

-- AlterTable
ALTER TABLE "LinkedAccount" ADD COLUMN     "name" TEXT,
ADD COLUMN     "profilePicture" TEXT,
ADD COLUMN     "screenName" TEXT;

-- DropTable
DROP TABLE "TwitterAccount";

-- AddForeignKey
ALTER TABLE "ScheduledTweet" ADD CONSTRAINT "ScheduledTweet_twitterAccountId_fkey" FOREIGN KEY ("twitterAccountId") REFERENCES "LinkedAccount"("id") ON DELETE CASCADE ON UPDATE CASCADE;
