// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  directUrl  = env("DIRECT_URL")
  extensions = [pgvector(map: "vector")]
}

model User {
  id                    String           @id @default(cuid())
  email                 String           @unique
  emailVerified         Boolean          @default(false)
  hashedPassword        String?
  name                  String?
  googleId             String?          @unique
  twitterId            String?          @unique
  profilePicture       String?
  bio                  String?
  location             String?
  website              String?
  preferences          Json?           // User preferences as JSON
  createdAt            DateTime         @default(now())
  updatedAt            DateTime         @updatedAt
  openAiApiKey         Bytes?          // Encrypted
  googleApiKey         Bytes?          // Encrypted
  defaultAiProvider    String          @default("GOOGLE") // OPENAI or GOOGLE
  defaultChatModel     String          @default("gemini-2.0-flash-exp")
  defaultEmbeddingModel String         @default("text-embedding-004")
  linkedAccounts       LinkedAccount[]
  botPersonas          BotPersona[]
  brainEntries         BrainEntry[]
  scheduledTweets      ScheduledTweet[]
  uploadedFiles        UploadedFile[]
  tokens               Token[]
  categories           BrainCategory[]

  @@index([email])
}

model LinkedAccount {
  id                String    @id @default(cuid())
  userId            String
  provider          String // "google", "twitter"
  providerAccountId String
  accessToken       Bytes? // Encrypted
  refreshToken      Bytes? // Encrypted
  expiresAt         Int?
  name              String?
  screenName        String? // For Twitter
  profilePicture    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  scheduledTweets ScheduledTweet[]

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model BotPersona {
  id              String           @id @default(cuid())
  userId          String
  name            String
  description     String?
  personaContent  Json            // Legacy: Stores parsed personality file
  personaType     PersonaType     @default(FILE)
  aiProvider      AIProvider      @default(OPENAI)
  chatModel       String?
  embeddingModel  String?
  
  // Enhanced persona fields
  system          String?         @db.Text // Main system prompt
  bio             Json?           // Array of bio options
  lore            Json?           // Array of background information
  messageExamples Json?           // Sample conversations
  postExamples    Json?           // Sample social media posts
  adjectives      Json?           // Descriptive words
  topics          Json?           // Subject areas
  style           Json?           // Guidelines for all content, chat, and posts
  version         Int             @default(1) // For future compatibility
  isActive        Boolean         @default(false) // Whether this is the active persona
  
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  scheduledTweets ScheduledTweet[]

  @@index([userId])
  @@index([isActive])
}

model ScheduledTweet {
  id              String         @id @default(cuid())
  userId          String
  content         String         @db.Text
  mediaUrlsJson   Json?         // Array of media URLs
  scheduledAt     DateTime       @db.Timestamptz
  status         TweetStatus    @default(PENDING)
  twitterAccountId String
  botPersonaId    String?
  publishedTweetId String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  twitterAccount  LinkedAccount @relation(fields: [twitterAccountId], references: [id], onDelete: Cascade)
  botPersona      BotPersona?   @relation(fields: [botPersonaId], references: [id])

  @@index([userId])
  @@index([scheduledAt])
  @@index([status])
}

model BrainEntry {
  id          String        @id @default(cuid())
  userId      String
  title       String?
  content     String        @db.Text
  sourceUrl   String?
  embedding   Unsupported("vector(1536)")
  categoryId  String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  category    BrainCategory? @relation(fields: [categoryId], references: [id])

  @@index([userId])
  @@index([categoryId])
}

model BrainCategory {
  id           String       @id @default(cuid())
  userId       String
  name         String
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  user         User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  brainEntries BrainEntry[]

  @@index([userId])
}

model UploadedFile {
  id        String   @id @default(cuid())
  userId    String
  fileName  String
  fileType  String
  fileSize  Int
  fileURL   String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Token {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  type      TokenType
  expiresAt DateTime
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([token])
}

enum TokenType {
  SESSION
  PASSWORD_RESET
}

enum TweetStatus {
  PENDING
  PUBLISHED
  FAILED
}

enum PersonaType {
  FILE
  SCRAPE
}

enum AIProvider {
  OPENAI
  GOOGLE
}

