"use client";

// Define UploadFileResponse type based on UploadThing documentation
type UploadFileResponse<TServerOutput = any> = {
  name: string;
  size: number;
  key: string;
  url: string;
  customId: string | null;
  serverData: TServerOutput;
};
import { UploadDropzone } from "@/lib/utils/uploadthing";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import Image from "next/image";

interface UploadedFile {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  fileURL: string;
  createdAt: Date;
}

interface MediaUploaderProps {
  onFilesUploaded: (files: UploadedFile[]) => void;
  existingFiles?: UploadedFile[];
  endpoint: "tweetMediaUploader";
  className?: string;
}

export function MediaUploader({
  onFilesUploaded,
  existingFiles = [],
  endpoint,
  className,
}: MediaUploaderProps) {
  const handleClientUploadComplete = (res?: UploadFileResponse<any>[]) => {
    if (!res) return;
    const uploaded = res.map((r) => r.serverData.file as UploadedFile);
    onFilesUploaded([...existingFiles, ...uploaded]);
  };
  
  const handleRemoveFile = (fileId: string) => {
    const newFiles = existingFiles.filter(file => file.id !== fileId);
    onFilesUploaded(newFiles);
  };

  const isImageFile = (fileType: string) => fileType.startsWith("image/");
  const isVideoFile = (fileType: string) => fileType.startsWith("video/");

  return (
    <div className={cn("space-y-4", className)}>
      <UploadDropzone
        endpoint={endpoint}
        onClientUploadComplete={handleClientUploadComplete}
        onUploadError={(error: Error) => {
          alert(`ERROR! ${error.message}`);
        }}
        config={{
          mode: "manual",
        }}
      />

      {existingFiles.length > 0 && (
        <div className={cn(
          "grid gap-2 pt-4",
          existingFiles.length === 1 ? "grid-cols-1" : "grid-cols-2"
        )}>
          {existingFiles.map((file) => (
            <div key={file.id} className="relative group overflow-hidden rounded-md border">
              {isImageFile(file.fileType) ? (
                <Image
                  src={file.fileURL}
                  alt={file.fileName}
                  width={400}
                  height={225}
                  className="w-full h-auto object-cover aspect-video"
                />
              ) : isVideoFile(file.fileType) ? (
                <video
                  src={file.fileURL}
                  controls
                  className="w-full h-auto object-cover aspect-video"
                />
              ) : (
                <div className="flex items-center justify-center aspect-video bg-muted">
                  <span className="text-sm text-muted-foreground">
                    {file.fileName}
                  </span>
                </div>
              )}
              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="absolute top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => handleRemoveFile(file.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 