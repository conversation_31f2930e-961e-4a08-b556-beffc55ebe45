"use client";

import { useState } from "react";
import { formatDistanceToNow } from "date-fns";
import { Heart, MessageSquare, Repeat2, Share } from "lucide-react";
import Image from "next/image";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface TweetPreviewProps {
  content: string;
  username?: string;
  handle?: string; // X handle
  avatar?: string;
  media?: string[]; // Array of media URLs
}

export function TweetPreview({
  content,
  username = "xtasker",
  handle = "@xtasker",
  avatar = "https://avatar.vercel.sh/xtasker.png",
  media,
}: TweetPreviewProps) {
  const [liked, setLiked] = useState(false);
  const [likes, setLikes] = useState(0);
  
  const handleLike = () => {
    if (liked) {
      setLikes(l => l - 1);
    } else {
      setLikes(l => l + 1);
    }
    setLiked(!liked);
  };

  const now = new Date();
  const timeAgo = formatDistanceToNow(now, { addSuffix: true });

  // Format content - convert hashtags to links
  const formattedContent = content.replace(
    /#(\w+)/g, 
    '<span class="text-blue-500 hover:underline">#$1</span>'
  );

  return (
    <div className="rounded-lg border bg-card shadow-sm transform transition-all hover:shadow-md overflow-hidden">
      <div className="p-4">
        <div className="flex items-start gap-3">
          <Avatar>
            <AvatarImage src={avatar} alt={username} />
            <AvatarFallback>{username.slice(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div className="flex-1 space-y-1.5">
            <div className="flex items-center gap-2">
              <p className="font-semibold">{username}</p>
              <p className="text-sm text-muted-foreground">{handle}</p>
              <p className="text-sm text-muted-foreground">·</p>
              <p className="text-sm text-muted-foreground">{timeAgo}</p>
            </div>
            <div 
              className="text-sm"
              dangerouslySetInnerHTML={{ __html: formattedContent || "Your tweet preview will appear here" }}
            />
            {media && media.length > 0 && (
              <div className="mt-3 grid grid-cols-1 gap-2 rounded-lg overflow-hidden">
                {media.map((src, index) => (
                  <div key={index} className="relative w-full h-auto max-h-[300px] overflow-hidden">
                    {src.startsWith('blob:video') || src.endsWith('.mp4') || src.endsWith('.mov') ? (
                      <video src={src} controls className="w-full h-full object-cover" />
                    ) : (
                      <Image
                        src={src}
                        alt={`Media preview ${index + 1}`}
                        width={400}
                        height={300}
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                ))}
              </div>
            )}
            {content && (
              <div className="flex items-center gap-6 pt-3">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Repeat2 className="h-4 w-4 text-muted-foreground" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={handleLike}
                >
                  <Heart
                    className={cn(
                      "h-4 w-4",
                      liked
                        ? "fill-red-500 text-red-500"
                        : "text-muted-foreground"
                    )}
                  />
                  {likes > 0 && <span className="ml-1 text-xs">{likes}</span>}
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Share className="h-4 w-4 text-muted-foreground" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}