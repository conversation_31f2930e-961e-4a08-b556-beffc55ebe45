"use client";

import { useState, useRef, useEffect } from "react";
import { Bold, Italic, Link, Hash, AtSign } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { EmojiPicker } from "./emoji-picker";
import { cn } from "@/lib/utils";

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  className?: string;
  disabled?: boolean;
}

export function RichTextEditor({
  value,
  onChange,
  placeholder = "What's happening?",
  maxLength = 280,
  className,
  disabled = false,
}: RichTextEditorProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [cursorPosition, setCursorPosition] = useState(0);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  }, [value]);

  const insertText = (textToInsert: string, wrapSelection = false) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    
    let newText: string;
    let newCursorPos: number;

    if (wrapSelection && selectedText) {
      // Wrap selected text
      newText = value.substring(0, start) + textToInsert + selectedText + textToInsert + value.substring(end);
      newCursorPos = start + textToInsert.length + selectedText.length + textToInsert.length;
    } else {
      // Insert at cursor position
      newText = value.substring(0, start) + textToInsert + value.substring(end);
      newCursorPos = start + textToInsert.length;
    }

    onChange(newText);
    
    // Restore cursor position
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  const handleEmojiSelect = (emoji: string) => {
    insertText(emoji);
  };

  const handleHashtag = () => {
    insertText("#");
  };

  const handleMention = () => {
    insertText("@");
  };

  const handleBold = () => {
    insertText("**", true);
  };

  const handleItalic = () => {
    insertText("*", true);
  };

  const handleLink = () => {
    const url = prompt("Enter URL:");
    if (url) {
      const textarea = textareaRef.current;
      if (!textarea) return;
      
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const selectedText = value.substring(start, end);
      
      const linkText = selectedText || "link";
      insertText(`[${linkText}](${url})`);
    }
  };

  const getCharacterCount = () => {
    // Count characters, treating URLs as 23 characters (Twitter's t.co length)
    const urlRegex = /https?:\/\/[^\s]+/g;
    const urls = value.match(urlRegex) || [];
    const urlCharacters = urls.reduce((total, url) => total + Math.max(23, url.length), 0);
    const nonUrlText = value.replace(urlRegex, '');
    return nonUrlText.length + urlCharacters;
  };

  const characterCount = getCharacterCount();
  const isOverLimit = characterCount > maxLength;

  // Highlight hashtags, mentions, and URLs
  const highlightText = (text: string) => {
    return text
      .replace(/#(\w+)/g, '<span class="text-blue-500 font-medium">#$1</span>')
      .replace(/@(\w+)/g, '<span class="text-blue-500 font-medium">@$1</span>')
      .replace(/(https?:\/\/[^\s]+)/g, '<span class="text-blue-500 underline">$1</span>');
  };

  return (
    <div className={cn("space-y-3", className)}>
      <div className="relative">
        {/* Hidden div for syntax highlighting preview */}
        <div 
          className="absolute inset-0 p-3 text-transparent pointer-events-none whitespace-pre-wrap break-words border border-transparent rounded-md"
          style={{ 
            font: 'inherit',
            lineHeight: 'inherit',
            zIndex: 1
          }}
          dangerouslySetInnerHTML={{ __html: highlightText(value) }}
        />
        
        {/* Actual textarea */}
        <Textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => {
            onChange(e.target.value);
            setCursorPosition(e.target.selectionStart);
          }}
          onSelect={(e) => setCursorPosition((e.target as HTMLTextAreaElement).selectionStart)}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            "relative z-10 bg-transparent resize-none min-h-[120px] max-h-[200px]",
            isOverLimit && "border-destructive focus-visible:ring-destructive"
          )}
          style={{ 
            caretColor: 'currentColor',
            color: 'transparent'
          }}
        />
        
        {/* Overlay for visible text */}
        <div 
          className="absolute inset-0 p-3 pointer-events-none whitespace-pre-wrap break-words"
          style={{ 
            font: 'inherit',
            lineHeight: 'inherit',
            zIndex: 5,
            color: 'currentColor'
          }}
        >
          {value}
        </div>
      </div>

      {/* Toolbar */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleBold}
            disabled={disabled}
            className="h-8 w-8 p-0"
          >
            <Bold className="h-4 w-4" />
          </Button>
          
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleItalic}
            disabled={disabled}
            className="h-8 w-8 p-0"
          >
            <Italic className="h-4 w-4" />
          </Button>
          
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleLink}
            disabled={disabled}
            className="h-8 w-8 p-0"
          >
            <Link className="h-4 w-4" />
          </Button>
          
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleHashtag}
            disabled={disabled}
            className="h-8 w-8 p-0"
          >
            <Hash className="h-4 w-4" />
          </Button>
          
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleMention}
            disabled={disabled}
            className="h-8 w-8 p-0"
          >
            <AtSign className="h-4 w-4" />
          </Button>
          
          <EmojiPicker onEmojiSelect={handleEmojiSelect} />
        </div>

        {/* Character count */}
        <div className="flex items-center gap-2">
          <span className={cn(
            "text-sm",
            isOverLimit ? "text-destructive" : "text-muted-foreground"
          )}>
            {characterCount}/{maxLength}
          </span>
          
          {/* Visual progress indicator */}
          <div className="relative w-8 h-8">
            <svg className="w-8 h-8 transform -rotate-90" viewBox="0 0 32 32">
              <circle
                cx="16"
                cy="16"
                r="14"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                className="text-muted-foreground/20"
              />
              <circle
                cx="16"
                cy="16"
                r="14"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeDasharray={`${2 * Math.PI * 14}`}
                strokeDashoffset={`${2 * Math.PI * 14 * (1 - Math.min(characterCount / maxLength, 1))}`}
                className={cn(
                  "transition-all duration-300",
                  characterCount < maxLength * 0.8 ? "text-green-500" :
                  characterCount < maxLength ? "text-yellow-500" : "text-destructive"
                )}
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
}
