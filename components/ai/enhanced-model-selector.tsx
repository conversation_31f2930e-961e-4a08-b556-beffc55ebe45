"use client";

import { useState, useEffect, useCallback } from "react";
import { Check, ChevronDown, Loader2, AlertCircle, Zap, Brain, Sparkles } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";

interface ModelInfo {
  id: string;
  name: string;
  provider: 'OPENAI' | 'GOOGLE';
  type: 'chat' | 'embedding';
  description?: string;
  contextLength?: number;
  inputCost?: number;
  outputCost?: number;
  isRecommended?: boolean;
  capabilities?: string[];
}

interface ProviderStatus {
  provider: 'OPENAI' | 'GOOGLE';
  isConfigured: boolean;
  chat?: string[];
  embedding?: string[];
}

interface EnhancedModelSelectorProps {
  provider: 'OPENAI' | 'GOOGLE';
  type: 'chat' | 'embedding';
  value?: string;
  onValueChange: (value: string) => void;
  apiKey?: string;
  disabled?: boolean;
  placeholder?: string;
}

// Enhanced model data with latest information
const MODEL_DATA: Record<string, ModelInfo[]> = {
  OPENAI: [
    {
      id: 'gpt-4o',
      name: 'GPT-4o',
      provider: 'OPENAI',
      type: 'chat',
      description: 'Most capable model, best for complex tasks',
      contextLength: 128000,
      inputCost: 2.50,
      outputCost: 10.00,
      isRecommended: true,
      capabilities: ['Vision', 'Function Calling', 'JSON Mode', 'Reasoning']
    },
    {
      id: 'gpt-4o-mini',
      name: 'GPT-4o Mini',
      provider: 'OPENAI',
      type: 'chat',
      description: 'Fast and cost-effective, great for most tasks',
      contextLength: 128000,
      inputCost: 0.15,
      outputCost: 0.60,
      isRecommended: true,
      capabilities: ['Vision', 'Function Calling', 'JSON Mode']
    },
    {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      provider: 'OPENAI',
      type: 'chat',
      description: 'Legacy model, still capable for simple tasks',
      contextLength: 16385,
      inputCost: 0.50,
      outputCost: 1.50,
      capabilities: ['Function Calling', 'JSON Mode']
    },
    {
      id: 'text-embedding-3-large',
      name: 'Text Embedding 3 Large',
      provider: 'OPENAI',
      type: 'embedding',
      description: 'Most capable embedding model',
      contextLength: 8191,
      inputCost: 0.13,
      isRecommended: true,
      capabilities: ['3072 dimensions', 'High accuracy']
    },
    {
      id: 'text-embedding-3-small',
      name: 'Text Embedding 3 Small',
      provider: 'OPENAI',
      type: 'embedding',
      description: 'Cost-effective embedding model',
      contextLength: 8191,
      inputCost: 0.02,
      capabilities: ['1536 dimensions', 'Good performance']
    },
  ],
  GOOGLE: [
    {
      id: 'gemini-2.0-flash-exp',
      name: 'Gemini 2.0 Flash (Experimental)',
      provider: 'GOOGLE',
      type: 'chat',
      description: 'Latest experimental model with advanced capabilities',
      contextLength: 1000000,
      inputCost: 0.075,
      outputCost: 0.30,
      isRecommended: true,
      capabilities: ['Vision', 'Audio', 'Function Calling', 'Long Context', 'Code Execution']
    },
    {
      id: 'gemini-1.5-pro',
      name: 'Gemini 1.5 Pro',
      provider: 'GOOGLE',
      type: 'chat',
      description: 'Most capable production model',
      contextLength: 2000000,
      inputCost: 1.25,
      outputCost: 5.00,
      isRecommended: true,
      capabilities: ['Vision', 'Audio', 'Function Calling', 'Long Context']
    },
    {
      id: 'gemini-1.5-flash',
      name: 'Gemini 1.5 Flash',
      provider: 'GOOGLE',
      type: 'chat',
      description: 'Fast and efficient for most tasks',
      contextLength: 1000000,
      inputCost: 0.075,
      outputCost: 0.30,
      capabilities: ['Vision', 'Audio', 'Function Calling', 'Long Context']
    },
    {
      id: 'text-embedding-004',
      name: 'Text Embedding 004',
      provider: 'GOOGLE',
      type: 'embedding',
      description: 'Latest embedding model',
      contextLength: 2048,
      inputCost: 0.00001,
      isRecommended: true,
      capabilities: ['768 dimensions', 'Multilingual']
    },
  ],
};

export function EnhancedModelSelector({
  provider,
  type,
  value,
  onValueChange,
  apiKey,
  disabled = false,
  placeholder = "Select a model...",
}: EnhancedModelSelectorProps) {
  const [open, setOpen] = useState(false);
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [filteredModels, setFilteredModels] = useState<ModelInfo[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Calculate similarity between two strings (basic implementation)
  const calculateSimilarity = (a: string, b: string): number => {
    const lowerA = a.toLowerCase();
    const lowerB = b.toLowerCase();

    if (lowerB.includes(lowerA)) return 1.0;
    if (lowerA.includes(lowerB)) return 0.9;

    // Count matching characters
    let matches = 0;
    for (const char of lowerA) {
      if (lowerB.includes(char)) matches++;
    }

    return matches / Math.max(lowerA.length, lowerB.length);
  };

  // Filter models based on search term with similarity ranking
  const filterModelsBySimilarity = useCallback((searchTerm: string, modelList: ModelInfo[]) => {
    if (!searchTerm.trim()) return modelList;

    return modelList.map(model => {
      // Calculate similarity scores on multiple fields
      const idScore = calculateSimilarity(searchTerm, model.id);
      const nameScore = calculateSimilarity(searchTerm, model.name);
      const descScore = model.description ? calculateSimilarity(searchTerm, model.description) : 0;

      // Use the highest match score
      const score = Math.max(idScore, nameScore, descScore);

      return {
        model,
        score
      };
    })
      .filter(item => item.score > 0.2) // Threshold for relevance
      .sort((a, b) => b.score - a.score) // Sort by score descending
      .map(item => item.model); // Extract just the models
  }, []);

  // Handle search input change
  const handleSearchChange = useCallback((term: string) => {
    setSearchTerm(term);
    setFilteredModels(filterModelsBySimilarity(term, models));
  }, [models, filterModelsBySimilarity]);

  const loadModels = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get static model data as fallback
      const staticModels = MODEL_DATA[provider]?.filter(model => model.type === type) || [];

      // If API key is provided (even just a flag that it's configured), try to fetch live models
      if (apiKey) {
        try {
          // Here we're using the /api/ai/config/models endpoint which securely fetches models
          // using the user's stored API key, so we don't need to send the actual key
          const response = await fetch('/api/ai/config/models');

          if (response.ok) {
            const data = await response.json();
            const providerData = (data.providers as ProviderStatus[]).find((p: ProviderStatus) => p.provider === provider);

            if (providerData && providerData.isConfigured) {
              // Select the right model list based on type
              const liveModels = type === 'chat' ? providerData.chat : providerData.embedding;

              if (liveModels && liveModels.length > 0) {
                // Merge live models with static data for rich UI
                const enhancedModels = liveModels.map((liveModel: string) => {
                  const staticModel = staticModels.find(m => m.id === liveModel);
                  return staticModel || {
                    id: liveModel,
                    name: liveModel,
                    provider,
                    type,
                    description: 'Available model',
                  };
                });
                setModels(enhancedModels);
                setFilteredModels(enhancedModels); // Set initial filtered models
                return; // Exit early if we successfully loaded live models
              }
            }
          }
          // Fall through to using static models if fetch fails or no models found
          console.warn('No live models found for', provider, type, '- using static data');
          setModels(staticModels);
          setFilteredModels(staticModels);
        } catch (fetchError) {
          console.warn('Failed to fetch live models, using static data:', fetchError);
          setModels(staticModels);
          setFilteredModels(staticModels);
        }
      } else {
        // No API key, use static models
        setModels(staticModels);
        setFilteredModels(staticModels);
      }
    } catch (err) {
      setError('Failed to load models');
      console.error('Error loading models:', err);
      setModels([]);
      setFilteredModels([]);
    } finally {
      setIsLoading(false);
    }
  }, [provider, type, apiKey]);

  useEffect(() => {
    loadModels();
  }, [loadModels]);

  const selectedModel = models.find(model => model.id === value);

  const formatCost = (cost?: number) => {
    if (!cost) return null;
    return cost < 1 ? `$${cost.toFixed(3)}` : `$${cost.toFixed(2)}`;
  };

  const getModelIcon = (model: ModelInfo) => {
    if (model.isRecommended) return <Sparkles className="h-4 w-4 text-yellow-500" />;
    if (model.capabilities?.includes('Vision')) return <Brain className="h-4 w-4 text-blue-500" />;
    return <Zap className="h-4 w-4 text-green-500" />;
  };

  if (isLoading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-2">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="w-full justify-between"
              disabled={disabled}
            >
              <div className="flex items-center gap-2">
                {selectedModel && getModelIcon(selectedModel)}
                <span className="truncate">
                  {selectedModel ? selectedModel.name : placeholder}
                </span>
              </div>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[400px] p-0">
            <Command>
              <CommandInput
                placeholder={`Search ${type} models...`}
                value={searchTerm}
                onValueChange={handleSearchChange}
              />
              <CommandEmpty>No models found matching your search.</CommandEmpty>
              <CommandList>
                <CommandGroup>
                  {filteredModels.map((model) => (
                    <CommandItem
                      key={model.id}
                      value={model.id}
                      onSelect={(currentValue) => {
                        onValueChange(currentValue === value ? "" : currentValue);
                        setOpen(false);
                      }}
                      className="flex flex-col items-start gap-2 p-3"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-2">
                          {getModelIcon(model)}
                          <span className="font-medium">{model.name}</span>
                          {model.isRecommended && (
                            <Badge variant="secondary" className="text-xs">
                              Recommended
                            </Badge>
                          )}
                        </div>
                        <Check
                          className={`h-4 w-4 ${value === model.id ? "opacity-100" : "opacity-0"
                            }`}
                        />
                      </div>

                      {model.description && (
                        <p className="text-sm text-muted-foreground">
                          {model.description}
                        </p>
                      )}

                      <div className="flex items-center gap-4 text-xs text-muted-foreground w-full">
                        {model.contextLength && (
                          <span>Context: {model.contextLength.toLocaleString()}</span>
                        )}
                        {model.inputCost && (
                          <span>Input: {formatCost(model.inputCost)}/1M tokens</span>
                        )}
                        {model.outputCost && (
                          <span>Output: {formatCost(model.outputCost)}/1M tokens</span>
                        )}
                      </div>

                      {model.capabilities && model.capabilities.length > 0 && (
                        <div className="flex flex-wrap gap-1 w-full">
                          {model.capabilities.slice(0, 3).map((capability) => (
                            <Badge key={capability} variant="outline" className="text-xs">
                              {capability}
                            </Badge>
                          ))}
                          {model.capabilities.length > 3 && (
                            <Tooltip>
                              <TooltipTrigger>
                                <Badge variant="outline" className="text-xs">
                                  +{model.capabilities.length - 3} more
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="max-w-xs">
                                  <p className="font-medium mb-1">All capabilities:</p>
                                  <p className="text-sm">{model.capabilities.join(', ')}</p>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {selectedModel && (
          <div className="text-sm text-muted-foreground">
            {selectedModel.description}
            {selectedModel.contextLength && (
              <span className="ml-2">
                • Context: {selectedModel.contextLength.toLocaleString()} tokens
              </span>
            )}
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}
