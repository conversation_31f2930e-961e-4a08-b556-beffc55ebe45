import React from 'react';
import { motion } from 'framer-motion';
import { Button, ButtonProps } from '@/components/ui/button';

export interface ButtonFeedbackProps extends ButtonProps {
  feedbackType?: 'scale' | 'pulse' | 'ripple';
  children: React.ReactNode;
}

/**
 * ButtonFeedback component for enhanced button interactions
 * 
 * @param feedbackType - Type of feedback animation
 * @param children - Button content
 * @param ...props - All standard button props
 */
export const ButtonFeedback: React.FC<ButtonFeedbackProps> = ({
  feedbackType = 'scale',
  children,
  ...props
}) => {
  // Define animation variants based on feedback type
  const getAnimationProps = () => {
    switch (feedbackType) {
      case 'scale':
        return {
          whileTap: { scale: 0.95 },
          whileHover: { scale: 1.02 },
          transition: { duration: 0.1 }
        };
      case 'pulse':
        return {
          whileTap: { scale: 0.95 },
          whileHover: {
            scale: [1, 1.02, 1.01],
            transition: {
              duration: 0.5,
              repeat: Infinity,
              repeatType: 'reverse' as const
            }
          }
        };
      case 'ripple':
        return {
          whileTap: {
            boxShadow: '0 0 0 4px rgba(99, 102, 241, 0.4)',
            transition: { duration: 0.2 }
          },
          whileHover: {
            boxShadow: '0 0 0 2px rgba(99, 102, 241, 0.2)',
            transition: { duration: 0.2 }
          }
        };
      default:
        return {
          whileTap: { scale: 0.95 },
          whileHover: { scale: 1.02 }
        };
    }
  };

  return (
    <motion.div
      className="inline-block"
      {...getAnimationProps()}
    >
      <Button {...props}>
        {children}
      </Button>
    </motion.div>
  );
};

export default ButtonFeedback;

