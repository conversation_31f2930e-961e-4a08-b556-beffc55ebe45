import React from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';

export interface HoverCardProps extends React.HTMLAttributes<HTMLDivElement> {
  hoverEffect?: 'lift' | 'glow' | 'border' | 'scale';
  hoverIntensity?: 'subtle' | 'medium' | 'strong';
  children: React.ReactNode;
}

/**
 * HoverCard component for enhanced card hover interactions
 * 
 * @param hoverEffect - Type of hover effect
 * @param hoverIntensity - Intensity of the hover effect
 * @param children - Card content
 * @param ...props - All standard card props
 */
export const HoverCard: React.FC<HoverCardProps> = ({
  hoverEffect = 'lift',
  hoverIntensity = 'medium',
  children,
  className,
  ...props
}) => {
  // Define intensity values
  const intensityValues = {
    lift: {
      subtle: { y: -2, shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' },
      medium: { y: -4, shadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)' },
      strong: { y: -8, shadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)' },
    },
    glow: {
      subtle: { boxShadow: '0 0 5px 2px rgba(99, 102, 241, 0.1)' },
      medium: { boxShadow: '0 0 10px 3px rgba(99, 102, 241, 0.15)' },
      strong: { boxShadow: '0 0 15px 5px rgba(99, 102, 241, 0.2)' },
    },
    border: {
      subtle: { borderColor: 'rgba(99, 102, 241, 0.3)' },
      medium: { borderColor: 'rgba(99, 102, 241, 0.5)' },
      strong: { borderColor: 'rgba(99, 102, 241, 0.8)' },
    },
    scale: {
      subtle: { scale: 1.01 },
      medium: { scale: 1.02 },
      strong: { scale: 1.05 },
    },
  };

  // Get hover animation properties
  const getHoverAnimation = () => {
    switch (hoverEffect) {
      case 'lift':
        return {
          y: 0,
          boxShadow: 'none',
          transition: { duration: 0.2 },
          whileHover: {
            y: intensityValues.lift[hoverIntensity].y,
            boxShadow: intensityValues.lift[hoverIntensity].shadow,
            transition: { duration: 0.2 },
          },
        };
      case 'glow':
        return {
          boxShadow: 'none',
          transition: { duration: 0.2 },
          whileHover: {
            boxShadow: intensityValues.glow[hoverIntensity].boxShadow,
            transition: { duration: 0.2 },
          },
        };
      case 'border':
        return {
          borderColor: 'transparent',
          transition: { duration: 0.2 },
          whileHover: {
            borderColor: intensityValues.border[hoverIntensity].borderColor,
            transition: { duration: 0.2 },
          },
        };
      case 'scale':
        return {
          scale: 1,
          transition: { duration: 0.2 },
          whileHover: {
            scale: intensityValues.scale[hoverIntensity].scale,
            transition: { duration: 0.2 },
          },
        };
      default:
        return {};
    }
  };

  // Add border class if using border effect
  const cardClassName = cn(
    className,
    hoverEffect === 'border' && 'border-2'
  );

  return (
    <motion.div {...getHoverAnimation()}>
      <Card className={cardClassName} {...props}>
        {children}
      </Card>
    </motion.div>
  );
};

export default HoverCard;
