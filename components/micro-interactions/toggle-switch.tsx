import React from 'react';
import { motion } from 'framer-motion';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import * as SwitchPrimitives from '@radix-ui/react-switch';

export interface ToggleSwitchProps extends React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root> {
  label?: string;
  labelPosition?: 'left' | 'right';
  animationType?: 'slide' | 'bounce' | 'spring';
}

/**
 * ToggleSwitch component for enhanced switch interactions
 * 
 * @param label - Optional label for the switch
 * @param labelPosition - Position of the label relative to the switch
 * @param animationType - Type of animation for the switch
 * @param ...props - All standard switch props
 */
export const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  label,
  labelPosition = 'right',
  animationType = 'slide',
  className,
  checked,
  ...props
}) => {
  // Define animation based on type
  const getAnimationProps = () => {
    switch (animationType) {
      case 'bounce':
        return {
          initial: { scale: 1 },
          animate: { scale: checked ? [1, 1.2, 1] : 1 },
          transition: { duration: 0.3 }
        };
      case 'spring':
        return {
          initial: { rotate: 0 },
          animate: { rotate: checked ? [0, 15, 0] : 0 },
          transition: {
            type: 'spring',
            stiffness: 300,
            damping: 10
          }
        };
      case 'slide':
      default:
        return {
          initial: { x: 0 },
          animate: { x: checked ? 3 : 0 },
          transition: { duration: 0.2 }
        };
    }
  };

  return (
    <div className={cn(
      "flex items-center gap-2",
      labelPosition === 'left' ? 'flex-row-reverse' : 'flex-row',
      className
    )}>
      <motion.div {...getAnimationProps()}>
        <Switch checked={checked} {...props} />
      </motion.div>

      {label && (
        <motion.label
          className="text-sm cursor-pointer select-none"
          animate={{
            color: checked ? 'var(--primary)' : 'var(--muted-foreground)'
          }}
          transition={{ duration: 0.2 }}
        >
          {label}
        </motion.label>
      )}
    </div>
  );
};

export default ToggleSwitch;
