import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Input, InputProps } from '@/components/ui/input';
import { cn } from '@/lib/utils';

export interface InputFocusProps extends InputProps {
  containerClassName?: string;
  focusHighlightColor?: string;
  label?: string;
}

/**
 * InputFocus component for enhanced input field interactions
 * 
 * @param containerClassName - Additional CSS classes for the container
 * @param focusHighlightColor - Color for the focus highlight effect
 * @param label - Optional label for the input
 * @param ...props - All standard input props
 */
export const InputFocus: React.FC<InputFocusProps> = ({
  containerClassName,
  focusHighlightColor = 'bg-primary/10',
  label,
  className,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <div className={cn("relative", containerClassName)}>
      {label && (
        <motion.label
          className="block text-sm font-medium mb-1 transition-all duration-200"
          animate={{
            color: isFocused ? 'var(--primary)' : 'var(--muted-foreground)',
            y: isFocused ? -2 : 0
          }}
        >
          {label}
        </motion.label>
      )}
      
      <div className="relative">
        <Input
          className={cn("transition-all duration-200", className)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />
        
        <motion.div
          className={cn(
            "absolute bottom-0 left-0 h-0.5 w-full origin-left",
            focusHighlightColor
          )}
          initial={{ scaleX: 0 }}
          animate={{ scaleX: isFocused ? 1 : 0 }}
          transition={{ duration: 0.2 }}
        />
      </div>
    </div>
  );
};

export default InputFocus;
