import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import Skeleton from './skeleton';

export interface TableSkeletonProps {
  rows?: number;
  columns?: number;
  hasHeader?: boolean;
  className?: string;
}

/**
 * TableSkeleton component for table loading states
 * 
 * @param rows - Number of rows to display
 * @param columns - Number of columns to display
 * @param hasHeader - Whether to show a header row
 * @param className - Additional CSS classes
 */
export const TableSkeleton: React.FC<TableSkeletonProps> = ({
  rows = 5,
  columns = 4,
  hasHeader = true,
  className,
}) => {
  return (
    <div className={className} data-testid="skeleton-table">
      <Table>
        {hasHeader && (
          <TableHeader>
            <TableRow>
              {Array.from({ length: columns }).map((_, index) => (
                <TableHead key={`header-${index}`}>
                  <Skeleton 
                    variant="text" 
                    className="h-5 w-24" 
                    data-testid={`skeleton-table-header-${index}`}
                  />
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
        )}
        
        <TableBody>
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <TableRow key={`row-${rowIndex}`}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <TableCell key={`cell-${rowIndex}-${colIndex}`}>
                  <Skeleton 
                    variant="text" 
                    className={`h-4 w-${Math.floor(Math.random() * 4) + 7}/12`} 
                    data-testid={`skeleton-table-cell-${rowIndex}-${colIndex}`}
                  />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default TableSkeleton;

