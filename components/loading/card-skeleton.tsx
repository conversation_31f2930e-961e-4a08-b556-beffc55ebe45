import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter } from '@/components/ui/card';
import Skeleton from './skeleton';

export interface CardSkeletonProps {
  hasHeader?: boolean;
  hasFooter?: boolean;
  contentLines?: number;
  className?: string;
}

/**
 * CardSkeleton component for card loading states
 * 
 * @param hasHeader - Whether to show a header skeleton
 * @param hasFooter - Whether to show a footer skeleton
 * @param contentLines - Number of content lines to show
 * @param className - Additional CSS classes
 */
export const CardSkeleton: React.FC<CardSkeletonProps> = ({
  hasHeader = true,
  hasFooter = false,
  contentLines = 3,
  className,
}) => {
  return (
    <Card className={className} data-testid="skeleton-card">
      {hasHeader && (
        <CardHeader className="space-y-2">
          <Skeleton 
            variant="text" 
            className="h-6 w-1/2" 
            data-testid="skeleton-card-title"
          />
          <Skeleton 
            variant="text" 
            className="h-4 w-3/4" 
            data-testid="skeleton-card-description"
          />
        </CardHeader>
      )}
      
      <CardContent className="space-y-2">
        {Array.from({ length: contentLines }).map((_, index) => (
          <Skeleton 
            key={index} 
            variant="text" 
            className={`h-4 w-${Math.floor(Math.random() * 4) + 7}/12`} 
            data-testid={`skeleton-card-content-${index}`}
          />
        ))}
      </CardContent>
      
      {hasFooter && (
        <CardFooter className="flex justify-between">
          <Skeleton 
            variant="rounded" 
            className="h-9 w-24" 
            data-testid="skeleton-card-footer-button-1"
          />
          <Skeleton 
            variant="rounded" 
            className="h-9 w-24" 
            data-testid="skeleton-card-footer-button-2"
          />
        </CardFooter>
      )}
    </Card>
  );
};

export default CardSkeleton;

