import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardContent } from '@/components/ui/card';
import Skeleton from './skeleton';

export interface PersonaSkeletonProps {
  showActions?: boolean;
  showBadges?: boolean;
  className?: string;
}

/**
 * PersonaSkeleton component for persona card loading states
 * 
 * @param showActions - Whether to show action button skeletons
 * @param showBadges - Whether to show badge skeletons
 * @param className - Additional CSS classes
 */
export const PersonaSkeleton: React.FC<PersonaSkeletonProps> = ({
  showActions = true,
  showBadges = true,
  className,
}) => {
  return (
    <Card className={className} data-testid="skeleton-persona">
      <CardHeader className="space-y-2">
        <div className="flex items-center gap-3">
          <Skeleton 
            variant="circular" 
            width={40} 
            height={40} 
            data-testid="skeleton-persona-avatar"
          />
          <div className="space-y-1 flex-1">
            <Skeleton 
              variant="text" 
              className="h-5 w-3/4" 
              data-testid="skeleton-persona-name"
            />
            <Skeleton 
              variant="text" 
              className="h-4 w-1/2" 
              data-testid="skeleton-persona-date"
            />
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Skeleton 
            variant="text" 
            className="h-4 w-11/12" 
            data-testid="skeleton-persona-bio-1"
          />
          <Skeleton 
            variant="text" 
            className="h-4 w-10/12" 
            data-testid="skeleton-persona-bio-2"
          />
          <Skeleton 
            variant="text" 
            className="h-4 w-8/12" 
            data-testid="skeleton-persona-bio-3"
          />
        </div>
        
        {showBadges && (
          <div className="flex flex-wrap gap-2 mt-3">
            <Skeleton 
              variant="rounded" 
              className="h-6 w-16" 
              data-testid="skeleton-persona-badge-1"
            />
            <Skeleton 
              variant="rounded" 
              className="h-6 w-20" 
              data-testid="skeleton-persona-badge-2"
            />
            <Skeleton 
              variant="rounded" 
              className="h-6 w-14" 
              data-testid="skeleton-persona-badge-3"
            />
          </div>
        )}
        
        {showActions && (
          <div className="flex justify-end gap-2 mt-4">
            <Skeleton 
              variant="rounded" 
              className="h-9 w-9" 
              data-testid="skeleton-persona-action-1"
            />
            <Skeleton 
              variant="rounded" 
              className="h-9 w-9" 
              data-testid="skeleton-persona-action-2"
            />
            <Skeleton 
              variant="rounded" 
              className="h-9 w-9" 
              data-testid="skeleton-persona-action-3"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PersonaSkeleton;

