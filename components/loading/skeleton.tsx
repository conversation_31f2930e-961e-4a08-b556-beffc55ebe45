import React from 'react';
import { cn } from '@/lib/utils';

export interface SkeletonProps {
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded';
  width?: string | number;
  height?: string | number;
  animate?: boolean;
  className?: string;
  'data-testid'?: string;
}

/**
 * Skeleton component for loading placeholders
 * 
 * @param variant - Shape variant of the skeleton
 * @param width - Width of the skeleton
 * @param height - Height of the skeleton
 * @param animate - Whether to show animation
 * @param className - Additional CSS classes
 * @param data-testid - Test ID for testing
 */
export const Skeleton: React.FC<SkeletonProps> = ({
  variant = 'text',
  width,
  height,
  animate = true,
  className,
  'data-testid': testId = 'skeleton-component',
}) => {
  // Get variant-specific classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'circular':
        return 'rounded-full';
      case 'rectangular':
        return 'rounded-none';
      case 'rounded':
        return 'rounded-md';
      case 'text':
      default:
        return 'rounded h-4';
    }
  };

  // Style object for width and height
  const style: React.CSSProperties = {};
  if (width) style.width = width;
  if (height) style.height = height;

  return (
    <div
      className={cn(
        'bg-gray-200 dark:bg-gray-700',
        animate && 'animate-pulse',
        getVariantClasses(),
        className
      )}
      style={style}
      data-testid={testId}
    />
  );
};

export default Skeleton;

