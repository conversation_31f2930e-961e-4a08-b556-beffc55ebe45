import React from 'react';
import { motion } from 'framer-motion';

export interface ScaleProps {
  children: React.ReactNode;
  initialScale?: number;
  finalScale?: number;
  springConfig?: {
    stiffness?: number;
    damping?: number;
    mass?: number;
  };
  whileHover?: {
    scale?: number;
    transition?: {
      duration?: number;
    };
  };
  whileTap?: {
    scale?: number;
  };
  className?: string;
}

/**
 * Scale component for scale animations with interactive states
 * 
 * @param children - Content to animate
 * @param initialScale - Initial scale value
 * @param finalScale - Final scale value
 * @param springConfig - Spring physics configuration
 * @param whileHover - Animation properties for hover state
 * @param whileTap - Animation properties for tap state
 * @param className - Additional CSS classes
 */
export const Scale: React.FC<ScaleProps> = ({
  children,
  initialScale = 0.9,
  finalScale = 1,
  springConfig,
  whileHover = { scale: 1.05 },
  whileTap = { scale: 0.95 },
  className = '',
}) => {
  // Get transition configuration
  const getTransition = () => {
    if (springConfig) {
      return {
        type: 'spring' as const,
        stiffness: springConfig.stiffness || 300,
        damping: springConfig.damping || 20,
        mass: springConfig.mass || 1,
      };
    }

    return {
      duration: 0.4,
      ease: 'easeOut' as const,
    };
  };

  // Animation variants
  const variants = {
    hidden: {
      scale: initialScale,
      opacity: 0,
    },
    visible: {
      scale: finalScale,
      opacity: 1,
      transition: getTransition(),
    },
  };

  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      whileHover={whileHover}
      whileTap={whileTap}
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

export default Scale;

