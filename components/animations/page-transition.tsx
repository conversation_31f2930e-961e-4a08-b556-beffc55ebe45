import React from 'react';
import { motion } from 'framer-motion';

export interface PageTransitionProps {
  children: React.ReactNode;
  transitionType?: 'fade' | 'slide' | 'scale';
  duration?: number;
  className?: string;
}

/**
 * PageTransition component for smooth transitions between pages
 * 
 * @param children - Content to animate
 * @param transitionType - Type of transition animation
 * @param duration - Animation duration in seconds
 * @param className - Additional CSS classes
 */
export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  transitionType = 'fade',
  duration = 0.5,
  className = '',
}) => {
  // Get animation variants based on transition type
  const getVariants = () => {
    switch (transitionType) {
      case 'slide':
        return {
          initial: { x: 300, opacity: 0 },
          animate: { 
            x: 0, 
            opacity: 1,
            transition: {
              duration,
              ease: 'easeOut',
            }
          },
          exit: { 
            x: -300, 
            opacity: 0,
            transition: {
              duration,
              ease: 'easeIn',
            }
          },
        };
      case 'scale':
        return {
          initial: { scale: 0.8, opacity: 0 },
          animate: { 
            scale: 1, 
            opacity: 1,
            transition: {
              duration,
              ease: 'easeOut',
            }
          },
          exit: { 
            scale: 0.8, 
            opacity: 0,
            transition: {
              duration,
              ease: 'easeIn',
            }
          },
        };
      case 'fade':
      default:
        return {
          initial: { opacity: 0 },
          animate: { 
            opacity: 1,
            transition: {
              duration,
              ease: 'easeOut',
            }
          },
          exit: { 
            opacity: 0,
            transition: {
              duration,
              ease: 'easeIn',
            }
          },
        };
    }
  };

  return (
    <motion.div
      className={className}
      initial="initial"
      animate="animate"
      exit="exit"
      variants={getVariants()}
    >
      {children}
    </motion.div>
  );
};

export default PageTransition;

