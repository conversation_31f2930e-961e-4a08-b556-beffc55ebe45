import React from 'react';
import { motion } from 'framer-motion';

export interface SlideProps {
  children: React.ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  duration?: number;
  delay?: number;
  springConfig?: {
    stiffness?: number;
    damping?: number;
    mass?: number;
  };
  className?: string;
}

/**
 * Slide component for sliding animations with optional spring physics
 * 
 * @param children - Content to animate
 * @param direction - Direction of the slide animation
 * @param duration - Animation duration in seconds (ignored if springConfig is provided)
 * @param delay - Animation delay in seconds
 * @param springConfig - Spring physics configuration
 * @param className - Additional CSS classes
 */
export const Slide: React.FC<SlideProps> = ({
  children,
  direction = 'up',
  duration = 0.5,
  delay = 0,
  springConfig,
  className = '',
}) => {
  // Get initial position based on direction
  const getInitialPosition = () => {
    switch (direction) {
      case 'up':
        return { y: 100 };
      case 'down':
        return { y: -100 };
      case 'left':
        return { x: 100 };
      case 'right':
        return { x: -100 };
      default:
        return { y: 100 };
    }
  };

  // Get transition configuration
  const getTransition = () => {
    if (springConfig) {
      return {
        type: 'spring',
        stiffness: springConfig.stiffness || 100,
        damping: springConfig.damping || 15,
        mass: springConfig.mass || 1,
        delay,
      };
    }
    
    return {
      duration,
      delay,
      ease: 'easeOut',
    };
  };

  // Animation variants
  const variants = {
    hidden: {
      ...getInitialPosition(),
      opacity: 0,
    },
    visible: {
      x: 0,
      y: 0,
      opacity: 1,
      transition: getTransition(),
    },
  };

  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

export default Slide;

