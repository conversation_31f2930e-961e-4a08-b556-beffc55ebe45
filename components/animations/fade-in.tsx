import React from 'react';
import { motion } from 'framer-motion';

export interface FadeInProps {
  children: React.ReactNode;
  duration?: number;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  distance?: number;
  once?: boolean;
  className?: string;
}

/**
 * FadeIn component for smooth fade animations with optional direction
 * 
 * @param children - Content to animate
 * @param duration - Animation duration in seconds
 * @param delay - Animation delay in seconds
 * @param direction - Direction of the fade animation
 * @param distance - Distance to move during animation (in pixels)
 * @param once - Whether to animate only once (true) or every time the element enters viewport (false)
 * @param className - Additional CSS classes
 */
export const FadeIn: React.FC<FadeInProps> = ({
  children,
  duration = 0.5,
  delay = 0,
  direction = 'up',
  distance = 20,
  once = true,
  className = '',
}) => {
  // Set initial and animate values based on direction
  const getDirectionalValues = () => {
    switch (direction) {
      case 'up':
        return { y: distance };
      case 'down':
        return { y: -distance };
      case 'left':
        return { x: distance };
      case 'right':
        return { x: -distance };
      case 'none':
      default:
        return {};
    }
  };

  // Animation variants
  const variants = {
    hidden: {
      opacity: 0,
      ...getDirectionalValues(),
    },
    visible: {
      opacity: 1,
      y: 0,
      x: 0,
      transition: {
        duration,
        delay,
        ease: "easeOut" as const,
      },
    },
  };

  return (
    <motion.div
      className={className}
      initial="hidden"
      whileInView="visible"
      viewport={{ once }}
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

export default FadeIn;

