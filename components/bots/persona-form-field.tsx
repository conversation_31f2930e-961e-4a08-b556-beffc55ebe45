"use client"

import * as React from "react"
import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Plus, X, HelpCircle } from "lucide-react"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface PersonaFormFieldProps {
  label: string
  description?: string
  type: "text" | "textarea" | "array" | "messageExamples"
  value: any
  onChange: (value: any) => void
  placeholder?: string
  required?: boolean
  className?: string
}

export function PersonaFormField({
  label,
  description,
  type,
  value,
  onChange,
  placeholder,
  required = false,
  className,
}: PersonaFormFieldProps) {
  const [newItem, setNewItem] = useState("")
  const [newMessageUser, setNewMessageUser] = useState("")
  const [newMessageAssistant, setNewMessageAssistant] = useState("")

  // Handle array item add
  const handleAddItem = () => {
    if (!newItem.trim()) return
    
    const updatedArray = Array.isArray(value) ? [...value, newItem] : [newItem]
    onChange(updatedArray)
    setNewItem("")
  }

  // Handle array item remove
  const handleRemoveItem = (index: number) => {
    if (!Array.isArray(value)) return
    
    const updatedArray = [...value]
    updatedArray.splice(index, 1)
    onChange(updatedArray)
  }

  // Handle message example add
  const handleAddMessageExample = () => {
    if (!newMessageUser.trim() || !newMessageAssistant.trim()) return
    
    const newExample = [
      { user: "user", content: { text: newMessageUser } },
      { user: "assistant", content: { text: newMessageAssistant } }
    ]
    
    const updatedExamples = Array.isArray(value) ? [...value, newExample] : [newExample]
    onChange(updatedExamples)
    
    setNewMessageUser("")
    setNewMessageAssistant("")
  }

  // Handle message example remove
  const handleRemoveMessageExample = (index: number) => {
    if (!Array.isArray(value)) return
    
    const updatedExamples = [...value]
    updatedExamples.splice(index, 1)
    onChange(updatedExamples)
  }

  return (
    <div className={className}>
      <div className="flex items-center mb-2">
        <Label htmlFor={label} className="text-sm font-medium">
          {label} {required && <span className="text-destructive">*</span>}
        </Label>
        
        {description && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-5 w-5 ml-1">
                  <HelpCircle className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">{description}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      {type === "text" && (
        <Input
          id={label}
          value={value || ""}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          required={required}
        />
      )}

      {type === "textarea" && (
        <Textarea
          id={label}
          value={value || ""}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          required={required}
          className="min-h-[100px]"
        />
      )}

      {type === "array" && (
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Input
              value={newItem}
              onChange={(e) => setNewItem(e.target.value)}
              placeholder={placeholder || `Add ${label.toLowerCase()}...`}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault()
                  handleAddItem()
                }
              }}
            />
            <Button type="button" onClick={handleAddItem} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-2 mt-2">
            {Array.isArray(value) && value.map((item, index) => (
              <Badge key={index} variant="secondary" className="px-2 py-1">
                {item}
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 ml-1"
                  onClick={() => handleRemoveItem(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
            
            {(!Array.isArray(value) || value.length === 0) && (
              <p className="text-sm text-muted-foreground">No items added yet.</p>
            )}
          </div>
        </div>
      )}

      {type === "messageExamples" && (
        <div className="space-y-4">
          <div className="space-y-2 border rounded-md p-3">
            <div className="space-y-2">
              <Label htmlFor={`${label}-user`} className="text-xs">
                User Message
              </Label>
              <Textarea
                id={`${label}-user`}
                value={newMessageUser}
                onChange={(e) => setNewMessageUser(e.target.value)}
                placeholder="Enter user message..."
                className="min-h-[80px]"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor={`${label}-assistant`} className="text-xs">
                Assistant Response
              </Label>
              <Textarea
                id={`${label}-assistant`}
                value={newMessageAssistant}
                onChange={(e) => setNewMessageAssistant(e.target.value)}
                placeholder="Enter assistant response..."
                className="min-h-[80px]"
              />
            </div>
            
            <Button
              type="button"
              onClick={handleAddMessageExample}
              disabled={!newMessageUser.trim() || !newMessageAssistant.trim()}
              className="w-full mt-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Message Example
            </Button>
          </div>
          
          <div className="space-y-4">
            {Array.isArray(value) && value.map((example, index) => (
              <div key={index} className="border rounded-md p-3 relative">
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 h-6 w-6"
                  onClick={() => handleRemoveMessageExample(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
                
                <div className="space-y-2">
                  <div className="bg-muted/50 p-2 rounded-md">
                    <p className="text-xs font-medium mb-1">User:</p>
                    <p className="text-sm">{example[0]?.content?.text}</p>
                  </div>
                  
                  <div className="bg-primary/10 p-2 rounded-md">
                    <p className="text-xs font-medium mb-1">Assistant:</p>
                    <p className="text-sm">{example[1]?.content?.text}</p>
                  </div>
                </div>
              </div>
            ))}
            
            {(!Array.isArray(value) || value.length === 0) && (
              <p className="text-sm text-muted-foreground">No message examples added yet.</p>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

