"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { BotPersona } from "@prisma/client"
import { But<PERSON> } from "@/components/ui/button"

// Enhanced BotPersona interface - use the base type directly since it already has the enhanced fields
type EnhancedBotPersona = BotPersona;
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Bot,
  ChevronDown,
  Settings,
  Plus,
  Sparkles,
  Check,
  Info
} from "lucide-react"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"

interface PersonaSwitcherProps {
  onPersonaChange?: (persona: EnhancedBotPersona) => void
  className?: string
}

export function PersonaSwitcher({
  onPersonaChange,
  className
}: PersonaSwitcherProps) {
  // State
  const [personas, setPersonas] = useState<EnhancedBotPersona[]>([])
  const [activePersona, setActivePersona] = useState<EnhancedBotPersona | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showDetails, setShowDetails] = useState(false)
  const [switchingTo, setSwitchingTo] = useState<string | null>(null)

  // Fetch personas on mount
  useEffect(() => {
    fetchPersonas()
  }, [])

  // Fetch personas from API
  const fetchPersonas = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch active persona
      const activeResponse = await fetch("/api/bots/personas?active=true")

      if (activeResponse.ok) {
        const activeData = await activeResponse.json()
        if (activeData.personas && activeData.personas.length > 0) {
          setActivePersona(activeData.personas[0])
        }
      }

      // Fetch all personas
      const response = await fetch("/api/bots/personas")

      if (!response.ok) {
        throw new Error("Failed to fetch personas")
      }

      const data = await response.json()
      setPersonas(data.personas || [])
    } catch (error) {
      console.error("Error fetching personas:", error)
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
    } finally {
      setLoading(false)
    }
  }

  // Handle persona selection (set as active)
  const handleSelectPersona = async (persona: EnhancedBotPersona) => {
    try {
      setError(null)
      setSwitchingTo(persona.id)

      const response = await fetch(`/api/bots/personas/${persona.id}/active`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }
      })

      if (!response.ok) {
        throw new Error("Failed to set active persona")
      }

      setActivePersona(persona)

      // Call the onPersonaChange callback
      if (onPersonaChange) {
        onPersonaChange(persona)
      }

      // Refresh personas to update UI
      await fetchPersonas()
    } catch (error) {
      console.error("Error setting active persona:", error)
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
    } finally {
      setSwitchingTo(null)
    }
  }

  // Get the first letter of the persona name for the avatar fallback
  const getAvatarFallback = (name: string) => {
    return name.charAt(0).toUpperCase()
  }

  // Get the first bio entry if available
  const getBioText = (persona: EnhancedBotPersona): string => {
    if (!persona.bio) {
      return persona.description || 'No description';
    }
    
    if (Array.isArray(persona.bio) && persona.bio.length > 0) {
      return String(persona.bio[0]);
    }
    
    if (typeof persona.bio === 'string') {
      return persona.bio;
    }
    
    if (typeof persona.bio === 'object' && persona.bio !== null) {
      return JSON.stringify(persona.bio);
    }
    
    return String(persona.bio);
  }

  // Show persona details dialog
  const showPersonaDetails = (persona: EnhancedBotPersona) => {
    setActivePersona(persona)
    setShowDetails(true)
  }

  // Animation variants
  const dropdownItemVariants = {
    hidden: { opacity: 0, y: -5 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.05,
        duration: 0.2
      }
    }),
    exit: { opacity: 0, y: 5, transition: { duration: 0.1 } }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className={`flex items-center space-x-2 ${className}`}
            disabled={loading}
          >
            {loading ? (
              <>
                <Skeleton className="h-5 w-5 rounded-full" />
                <Skeleton className="h-4 w-24" />
                <ChevronDown className="h-4 w-4 opacity-50" />
              </>
            ) : activePersona ? (
              <>
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <Avatar className="h-5 w-5">
                    <AvatarFallback className="text-xs">
                      {getAvatarFallback(activePersona.name)}
                    </AvatarFallback>
                  </Avatar>
                </motion.div>
                <motion.span
                  className="max-w-[150px] truncate"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {activePersona.name}
                </motion.span>
                <ChevronDown className="h-4 w-4 opacity-50" />
              </>
            ) : (
              <>
                <Bot className="h-5 w-5" />
                <span>Select Persona</span>
                <ChevronDown className="h-4 w-4 opacity-50" />
              </>
            )}
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Bot Personas</DropdownMenuLabel>

          {personas.length > 0 ? (
            <AnimatePresence>
              {personas.map((persona, index) => (
                <motion.div
                  key={persona.id}
                  custom={index}
                  variants={dropdownItemVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                >
                  <DropdownMenuItem
                    className="flex items-center justify-between cursor-pointer"
                    onClick={() => handleSelectPersona(persona)}
                  >
                    <div className="flex items-center">
                      <Avatar className="h-6 w-6 mr-2">
                        <AvatarFallback className="text-xs">
                          {getAvatarFallback(persona.name)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="truncate">{persona.name}</span>
                    </div>

                    <div className="flex items-center">
                      {switchingTo === persona.id ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="h-4 w-4 text-primary mr-1"
                        >
                          <svg viewBox="0 0 24 24" fill="none" className="h-4 w-4">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        </motion.div>
                      ) : persona.id === activePersona?.id && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ type: "spring", stiffness: 500, damping: 15 }}
                        >
                          <Check className="h-4 w-4 text-primary mr-1" />
                        </motion.div>
                      )}
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={(e) => {
                                e.stopPropagation()
                                showPersonaDetails(persona)
                              }}
                            >
                              <Info className="h-3 w-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="left">
                            <p>View details</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </DropdownMenuItem>
                </motion.div>
              ))}
            </AnimatePresence>
          ) : (
            <div className="px-2 py-4 text-center">
              <p className="text-sm text-muted-foreground">No personas available</p>
            </div>
          )}

          <DropdownMenuSeparator />

          <Link href="/bots/personas" passHref>
            <DropdownMenuItem className="cursor-pointer">
              <Settings className="h-4 w-4 mr-2" />
              <span>Manage Personas</span>
            </DropdownMenuItem>
          </Link>

          <Link href="/bots/personas?create=true" passHref>
            <DropdownMenuItem className="cursor-pointer">
              <Plus className="h-4 w-4 mr-2" />
              <span>Create New</span>
            </DropdownMenuItem>
          </Link>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Persona Details Dialog */}
      <AnimatePresence>
        {showDetails && (
          <Dialog open={showDetails} onOpenChange={setShowDetails}>
            <DialogContent className="sm:max-w-md">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
              >
                <DialogHeader>
                  <DialogTitle>Persona Details</DialogTitle>
                  <DialogDescription>
                    Information about the selected persona
                  </DialogDescription>
                </DialogHeader>

                {activePersona && (
                  <Card className="border-0 shadow-none">
                    <CardHeader className="px-0 pt-0">
                      <div className="flex items-center space-x-2">
                        <motion.div
                          initial={{ scale: 0.8, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ delay: 0.1, duration: 0.3 }}
                        >
                          <Avatar>
                            <AvatarFallback>
                              {getAvatarFallback(activePersona.name)}
                            </AvatarFallback>
                          </Avatar>
                        </motion.div>
                        <div>
                          <CardTitle className="flex items-center">
                            {activePersona.name}
                            {activePersona.id === activePersona?.id && (
                              <motion.div
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.2, duration: 0.3 }}
                              >
                                <Badge variant="outline" className="ml-2 bg-primary/10 text-primary">
                                  <Sparkles className="mr-1 h-3 w-3" /> Active
                                </Badge>
                              </motion.div>
                            )}
                          </CardTitle>
                          <CardDescription>
                            {getBioText(activePersona)}
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="px-0 space-y-4">
                      {activePersona.system && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.3, duration: 0.3 }}
                        >
                          <h4 className="text-sm font-medium mb-1">System Prompt</h4>
                          <p className="text-sm text-muted-foreground">
                            {activePersona.system.length > 150
                              ? `${activePersona.system.substring(0, 150)}...`
                              : activePersona.system}
                          </p>
                        </motion.div>
                      )}

                      {activePersona.topics && Array.isArray(activePersona.topics) && activePersona.topics.length > 0 && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.4, duration: 0.3 }}
                        >
                          <h4 className="text-sm font-medium mb-1">Topics</h4>
                          <div className="flex flex-wrap gap-1">
                            {activePersona.topics.slice(0, 5).map((topic, index) => (
                              <motion.div
                                key={index}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: 0.4 + index * 0.05, duration: 0.2 }}
                              >
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {String(topic)}
                                </Badge>
                              </motion.div>
                            ))}
                            {activePersona.topics.length > 5 && (
                              <Badge variant="outline" className="text-xs">
                                +{activePersona.topics.length - 5} more
                              </Badge>
                            )}
                          </div>
                        </motion.div>
                      )}

                      {activePersona.adjectives && Array.isArray(activePersona.adjectives) && activePersona.adjectives.length > 0 && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.5, duration: 0.3 }}
                        >
                          <h4 className="text-sm font-medium mb-1">Personality</h4>
                          <div className="flex flex-wrap gap-1">
                            {activePersona.adjectives.slice(0, 5).map((adj, index) => (
                              <motion.div
                                key={index}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: 0.5 + index * 0.05, duration: 0.2 }}
                              >
                                <Badge key={index} variant="outline" className="text-xs">
                                  {String(adj)}
                                </Badge>
                              </motion.div>
                            ))}
                            {activePersona.adjectives.length > 5 && (
                              <Badge variant="outline" className="text-xs">
                                +{activePersona.adjectives.length - 5} more
                              </Badge>
                            )}
                          </div>
                        </motion.div>
                      )}
                    </CardContent>

                    <CardFooter className="px-0 pt-2 flex justify-between">
                      <Button
                        variant="outline"
                        onClick={() => setShowDetails(false)}
                      >
                        Close
                      </Button>

                      {activePersona.id !== activePersona?.id && (
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            onClick={() => {
                              handleSelectPersona(activePersona)
                              setShowDetails(false)
                            }}
                          >
                            Set as Active
                          </Button>
                        </motion.div>
                      )}
                    </CardFooter>
                  </Card>
                )}
              </motion.div>
            </DialogContent>
          </Dialog>
        )}
      </AnimatePresence>
    </>
  )
}
