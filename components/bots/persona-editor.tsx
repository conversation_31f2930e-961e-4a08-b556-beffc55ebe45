"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { BotPersona } from "@prisma/client"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { PersonaFormField } from "./persona-form-field"
import { EnhancedPersonaTemplate } from "@/lib/bots/types"
import { AlertCircle, Save, ArrowLeft, Plus, Trash } from "lucide-react"
import { useForm, Controller, useFieldArray } from "react-hook-form"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

// Enhanced BotPersona interface - use the base type directly since it already has the enhanced fields
type EnhancedBotPersona = BotPersona;

const normalizeJsonArray = (value: any): string[] => {
  if (!value) return []
  if (Array.isArray(value)) {
    return value.map(v => String(v)).filter(Boolean)
  }
  return [String(value)]
}

interface PersonaEditorProps {
  persona?: EnhancedBotPersona | null
  onSave: (personaData: EnhancedPersonaTemplate) => Promise<BotPersona | null>
  onCancel: () => void
  isLoading?: boolean
}

export function PersonaEditor({
  persona,
  onSave,
  onCancel,
  isLoading = false,
}: PersonaEditorProps) {
  // Form state
  const [formData, setFormData] = useState<EnhancedPersonaTemplate>({
    name: "",
    system: "",
    bio: [],
    lore: [],
    messageExamples: [],
    postExamples: [],
    adjectives: [],
    topics: [],
    style: {
      all: [],
      chat: [],
      post: []
    }
  })

  // UI state
  const [activeTab, setActiveTab] = useState("basic")
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize form with persona data if provided
  useEffect(() => {
    if (persona) {
      const styleData = (persona.style && typeof persona.style === 'object' && persona.style) || {}
      setFormData({
        name: persona.name || "",
        system: persona.system || "",
        bio: normalizeJsonArray(persona.bio),
        lore: normalizeJsonArray(persona.lore),
        messageExamples: normalizeJsonArray(persona.messageExamples),
        postExamples: normalizeJsonArray(persona.postExamples),
        adjectives: normalizeJsonArray(persona.adjectives),
        topics: normalizeJsonArray(persona.topics),
        style: {
          all: normalizeJsonArray((styleData as any).all),
          chat: normalizeJsonArray((styleData as any).chat),
          post: normalizeJsonArray((styleData as any).post),
        },
      })
    }
  }, [persona])

  // Handle form field change
  const handleFieldChange = (field: keyof EnhancedPersonaTemplate, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }))
  }

  // Handle style field change
  const handleStyleChange = (styleType: "all" | "chat" | "post", value: string[]) => {
    setFormData((prev) => ({
      ...prev,
      style: {
        ...prev.style,
        [styleType]: value
      }
    }))
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSaving(true)

    try {
      // Validate required fields
      if (!formData.name || !formData.name.trim()) {
        setError("Name is required")
        setSaving(false)
        return
      }

      if (!formData.system || !formData.system.trim()) {
        setError("System prompt is required")
        setSaving(false)
        return
      }

      if (!Array.isArray(formData.bio) || formData.bio.length === 0) {
        setError("At least one bio entry is required")
        setSaving(false)
        return
      }

      // Save the persona
      await onSave(formData)
    } catch (error) {
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
    } finally {
      setSaving(false)
    }
  }

  const {
    register,
    control,
    formState: { errors },
  } = useForm<EnhancedPersonaTemplate>({
    defaultValues: formData,
  });

  const { fields: bioFields, append: appendBio, remove: removeBio } = useFieldArray({
    control: control as any,
    name: "bio",
  });

  const { fields: loreFields, append: appendLore, remove: removeLore } = useFieldArray({
    control: control as any,
    name: "lore",
  });

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex justify-between items-center">
        <Button type="button" variant="ghost" onClick={onCancel}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h2 className="text-2xl font-bold">
          {persona ? "Edit Persona" : "Create New Persona"}
        </h2>
        <Button type="submit" disabled={saving || isLoading}>
          {saving || isLoading ? "Saving..." : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Persona
            </>
          )}
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="basic" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="examples">Examples</TabsTrigger>
          <TabsTrigger value="style">Style & Topics</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Define the core aspects of your persona
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Persona Details</h3>
                  <p className="text-sm text-muted-foreground">
                    Define your persona&quot;s identity and history
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        placeholder="Enter persona name"
                        {...register("name", { required: "Name is required" })}
                      />
                      {errors.name && (
                        <p className="text-sm text-destructive">{errors.name.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="system">System Prompt</Label>
                      <Controller
                        name="system"
                        control={control}
                        rules={{ required: "System prompt is required" }}
                        render={({ field }) => (
                          <Textarea
                            id="system"
                            placeholder="Enter system prompt for the AI"
                            className="min-h-[100px]"
                            {...field}
                          />
                        )}
                      />
                      {errors.system && (
                        <p className="text-sm text-destructive">{errors.system.message}</p>
                      )}
                      <p className="text-sm text-muted-foreground">
                        This is the main instruction that defines your persona&quot;s behavior
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Content & Background</CardTitle>
              <CardDescription>
                Define the persona&quot;s background and characteristics.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Bio & Background</h3>
                  <p className="text-sm text-muted-foreground">
                    Define your persona&apos;s identity and history
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Bio Options</Label>
                    <div className="space-y-2">
                      {bioFields.map((field, index) => (
                        <div key={field.id} className="flex gap-2">
                          <Input
                            placeholder="Enter a bio option"
                            {...register(`bio.${index}` as const)}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeBio(index)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => appendBio("")}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Bio Option
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Lore & Background</Label>
                    <div className="space-y-2">
                      {loreFields.map((field, index) => (
                        <div key={field.id} className="flex gap-2">
                          <Input
                            placeholder="Enter background information"
                            {...register(`lore.${index}` as const)}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeLore(index)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => appendLore("")}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Lore
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="examples" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Example Content</CardTitle>
              <CardDescription>
                Provide examples of how the persona should respond and create content.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <PersonaFormField
                label="Message Examples"
                description="Example conversations showing how the persona should respond"
                type="messageExamples"
                value={formData.messageExamples}
                onChange={(value) => handleFieldChange("messageExamples", value)}
              />

              <PersonaFormField
                label="Post Examples"
                description="Example social media posts that the persona might create"
                type="array"
                value={formData.postExamples}
                onChange={(value) => handleFieldChange("postExamples", value)}
                placeholder="Add a post example"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="style" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Style Guidelines</CardTitle>
              <CardDescription>
                Define how your persona should communicate
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <PersonaFormField
                label="Topics"
                description="Subjects the persona is knowledgeable about"
                type="array"
                value={formData.topics}
                onChange={(value) => handleFieldChange("topics", value)}
                placeholder="Add a topic"
              />

              <PersonaFormField
                label="Adjectives"
                description="Words that describe the persona&quot;s character"
                type="array"
                value={formData.adjectives}
                onChange={(value) => handleFieldChange("adjectives", value)}
                placeholder="Add an adjective"
              />

              <PersonaFormField
                label="General Style Guidelines"
                description="Rules that apply to all content"
                type="array"
                value={formData.style?.all || []}
                onChange={(value) => handleStyleChange("all", value)}
                placeholder="Add a style guideline"
              />

              <PersonaFormField
                label="Chat Style Guidelines"
                description="Rules specific to chat conversations"
                type="array"
                value={formData.style?.chat || []}
                onChange={(value) => handleStyleChange("chat", value)}
                placeholder="Add a chat guideline"
              />

              <PersonaFormField
                label="Post Style Guidelines"
                description="Rules specific to social media posts"
                type="array"
                value={formData.style?.post || []}
                onChange={(value) => handleStyleChange("post", value)}
                placeholder="Add a post guideline"
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <CardFooter className="flex justify-between pt-6">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={saving || isLoading}>
          {saving || isLoading ? "Saving..." : "Save Persona"}
        </Button>
      </CardFooter>
    </form>
  )
}
