"use client"

import * as React from "react"
import { useState } from "react"
import { Bot<PERSON>ersona } from "@prisma/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Check, Search, Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"

interface PersonaSelectorProps {
  personas: BotPersona[]
  activePersona: BotPersona | null
  onSelect: (persona: BotPersona) => void
  className?: string
}

export function PersonaSelector({
  personas,
  activePersona,
  onSelect,
  className
}: PersonaSelectorProps) {
  // State
  const [searchQuery, setSearchQuery] = useState("")
  
  // Filter personas based on search query
  const filteredPersonas = personas.filter(persona => 
    persona.name.toLowerCase().includes(searchQuery.toLowerCase())
  )
  
  return (
    <div className={cn("flex flex-col space-y-2", className)}>
      <div className="relative">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search personas..."
          className="pl-8"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      
      <ScrollArea className="h-[200px] rounded-md border">
        <div className="p-2 space-y-1">
          {filteredPersonas.length > 0 ? (
            filteredPersonas.map(persona => (
              <Button
                key={persona.id}
                variant={persona.id === activePersona?.id ? "default" : "ghost"}
                className={cn(
                  "w-full justify-start",
                  persona.id === activePersona?.id && "bg-primary text-primary-foreground"
                )}
                onClick={() => onSelect(persona)}
              >
                <div className="flex items-center w-full">
                  {persona.id === activePersona?.id ? (
                    <Check className="h-4 w-4 mr-2" />
                  ) : (
                    <Sparkles className="h-4 w-4 mr-2 opacity-70" />
                  )}
                  <span className="truncate">{persona.name}</span>
                </div>
              </Button>
            ))
          ) : (
            <div className="text-center py-4 text-sm text-muted-foreground">
              {searchQuery ? "No matching personas found" : "No personas available"}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

