"use client"

import * as React from "react"
import { useState } from "react"
import { BotPersona } from "@prisma/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { 
  Download, 
  Upload, 
  AlertCircle, 
  ChevronDown 
} from "lucide-react"
import { PersonaImportResult, PersonaExportResult } from "@/lib/bots/types"

interface PersonaImportExportProps {
  onImport: (jsonData: string) => Promise<PersonaImportResult>
  onExport: (persona: BotPersona) => Promise<PersonaExportResult>
  activePersona: BotPersona | null
}

export function PersonaImportExport({
  onImport,
  onExport,
  activePersona
}: PersonaImportExportProps) {
  // State
  const [importDialogOpen, setImportDialogOpen] = useState(false)
  const [exportDialogOpen, setExportDialogOpen] = useState(false)
  const [importData, setImportData] = useState("")
  const [exportData, setExportData] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showErrorDialog, setShowErrorDialog] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  
  // Handle import
  const handleImport = async () => {
    if (!importData.trim()) {
      setError("Please enter persona data to import")
      setShowErrorDialog(true)
      return
    }
    
    try {
      setLoading(true)
      setError(null)
      
      const result = await onImport(importData)
      
      if (!result.success) {
        throw new Error(result.error || "Failed to import persona")
      }
      
      setSuccess("Persona imported successfully")
      setImportData("")
      setImportDialogOpen(false)
    } catch (error) {
      console.error("Error importing persona:", error)
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
      setShowErrorDialog(true)
    } finally {
      setLoading(false)
    }
  }
  
  // Handle export
  const handleExport = async () => {
    if (!activePersona) {
      setError("No active persona to export")
      setShowErrorDialog(true)
      return
    }
    
    try {
      setLoading(true)
      setError(null)
      
      const result = await onExport(activePersona)
      
      if (!result.success) {
        throw new Error(result.error || "Failed to export persona")
      }
      
      setExportData(result.data || "")
      setExportDialogOpen(true)
    } catch (error) {
      console.error("Error exporting persona:", error)
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
      setShowErrorDialog(true)
    } finally {
      setLoading(false)
    }
  }
  
  // Handle copy to clipboard
  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(exportData)
    setSuccess("Copied to clipboard")
    setTimeout(() => setSuccess(null), 2000)
  }
  
  // Handle download as file
  const handleDownload = () => {
    const blob = new Blob([exportData], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `${activePersona?.name.replace(/\s+/g, "-").toLowerCase() || "persona"}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
  
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="gap-1">
            Actions
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DialogTrigger asChild onClick={() => setImportDialogOpen(true)}>
            <DropdownMenuItem>
              <Upload className="h-4 w-4 mr-2" />
              Import Persona
            </DropdownMenuItem>
          </DialogTrigger>
          <DropdownMenuItem 
            onClick={handleExport}
            disabled={!activePersona || loading}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Active Persona
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      
      {/* Import Dialog */}
      <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Import Persona</DialogTitle>
            <DialogDescription>
              Paste the JSON data of the persona you want to import.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Textarea
              placeholder="Paste persona JSON data here..."
              value={importData}
              onChange={(e) => setImportData(e.target.value)}
              className="min-h-[200px]"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setImportDialogOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleImport}
              disabled={loading || !importData.trim()}
            >
              {loading ? "Importing..." : "Import"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Export Dialog */}
      <Dialog open={exportDialogOpen} onOpenChange={setExportDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Export Persona: {activePersona?.name}</DialogTitle>
            <DialogDescription>
              Copy the JSON data or download as a file.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Textarea
              value={exportData}
              readOnly
              className="min-h-[200px] font-mono text-xs"
            />
          </div>
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <div className="flex-1 flex justify-start">
              {success && (
                <span className="text-sm text-green-500">{success}</span>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleCopyToClipboard}
              >
                Copy to Clipboard
              </Button>
              <Button onClick={handleDownload}>
                Download
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Error Dialog */}
      <AlertDialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-destructive" />
              Error
            </AlertDialogTitle>
            <AlertDialogDescription>
              {error || "An unexpected error occurred."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Dismiss</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

