"use client"

import * as React from "react"
import { <PERSON>t<PERSON>erson<PERSON> } from "@prisma/client"
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Check, Edit, Trash2, Download, Star } from "lucide-react"

// Enhanced BotPersona interface - use the base type directly since it already has the enhanced fields
type EnhancedBotPersona = BotPersona;

interface PersonaCardProps {
  persona: EnhancedBotPersona
  isActive?: boolean
  onSelect?: (persona: EnhancedBotPersona) => void
  onEdit?: (persona: EnhancedBotPersona) => void
  onDelete?: (persona: EnhancedBotPersona) => void
  onExport?: (persona: EnhancedBotPersona) => void
}

export function PersonaCard({
  persona,
  isActive = false,
  onSelect,
  onEdit,
  onDelete,
  onExport,
}: PersonaCardProps) {
  // Get the first letter of the persona name for the avatar fallback
  const fallbackInitial = persona.name.charAt(0).toUpperCase()

  // Get the first bio entry if available
  const bioText = persona.bio
    ? Array.isArray(persona.bio) && persona.bio.length > 0
      ? String(persona.bio[0])
      : typeof persona.bio === 'string'
        ? persona.bio
        : typeof persona.bio === 'object' && persona.bio !== null
          ? JSON.stringify(persona.bio)
          : String(persona.bio)
    : persona.description || 'No description'

  // Get topics for badges
  const topics = persona.topics
    ? Array.isArray(persona.topics) && persona.topics.length > 0
      ? persona.topics.slice(0, 3).map(topic => 
          typeof topic === 'string' ? topic : String(topic)
        ).filter(Boolean)
      : []
    : []

  return (
    <Card className={`w-full transition-all duration-200 ${isActive ? 'border-primary shadow-md' : ''}`}>
      <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-2">
        <div className="flex flex-row items-center space-x-2">
          <Avatar>
            <AvatarFallback>{fallbackInitial}</AvatarFallback>
          </Avatar>
          <div>
            <CardTitle className="text-lg">{persona.name}</CardTitle>
            <CardDescription className="text-sm truncate max-w-[200px]">
              {bioText}
            </CardDescription>
          </div>
        </div>
        {isActive && (
          <Badge variant="outline" className="bg-primary/10 text-primary">
            <Check className="mr-1 h-3 w-3" /> Active
          </Badge>
        )}
      </CardHeader>
      <CardContent className="pt-2">
        <div className="flex flex-wrap gap-1 mb-2">
          {topics.map((topic, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {topic}
            </Badge>
          ))}
        </div>
        <div className="text-sm text-muted-foreground mt-2">
          <p className="truncate">
            {persona.system
              ? `${persona.system.substring(0, 100)}${persona.system.length > 100 ? '...' : ''}`
              : 'Legacy persona format'}
          </p>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-2">
        <div className="flex space-x-1">
          <Button variant="outline" size="icon" onClick={() => onEdit?.(persona)}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={() => onDelete?.(persona)}>
            <Trash2 className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={() => onExport?.(persona)}>
            <Download className="h-4 w-4" />
          </Button>
        </div>
        <Button
          variant={isActive ? "secondary" : "default"}
          size="sm"
          onClick={() => onSelect?.(persona)}
          disabled={isActive}
        >
          {isActive ? (
            <>
              <Star className="mr-1 h-4 w-4" /> Active
            </>
          ) : (
            'Set Active'
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

