"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { BotPersona } from "@prisma/client"
import { PersonaSelector } from "./persona-selector"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from "@/components/ui/popover"
import { 
  <PERSON><PERSON><PERSON>, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { 
  Bot, 
  Plus, 
  Settings, 
  Sparkles,
  AlertCircle
} from "lucide-react"
import Link from "next/link"

export function PersonaHeaderSelector() {
  // State
  const [personas, setPersonas] = useState<BotPersona[]>([])
  const [activePersona, setActivePersona] = useState<BotPersona | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showErrorDialog, setShowErrorDialog] = useState(false)
  
  // Fetch personas on mount
  useEffect(() => {
    fetchPersonas()
  }, [])
  
  // Fetch personas from API
  const fetchPersonas = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Fetch active persona
      const activeResponse = await fetch("/api/bots/personas?active=true")
      
      if (activeResponse.ok) {
        const activeData = await activeResponse.json()
        if (activeData.personas && activeData.personas.length > 0) {
          setActivePersona(activeData.personas[0])
        }
      }
      
      // Fetch all personas
      const response = await fetch("/api/bots/personas")
      
      if (!response.ok) {
        throw new Error("Failed to fetch personas")
      }
      
      const data = await response.json()
      setPersonas(data.personas || [])
    } catch (error) {
      console.error("Error fetching personas:", error)
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
      setShowErrorDialog(true)
    } finally {
      setLoading(false)
    }
  }
  
  // Handle persona selection (set as active)
  const handleSelectPersona = async (persona: BotPersona) => {
    try {
      setError(null)
      
      const response = await fetch(`/api/bots/personas/${persona.id}/active`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }
      })
      
      if (!response.ok) {
        throw new Error("Failed to set active persona")
      }
      
      setActivePersona(persona)
      
      // Refresh personas to update UI
      await fetchPersonas()
    } catch (error) {
      console.error("Error setting active persona:", error)
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
      setShowErrorDialog(true)
    }
  }

  return (
    <>
      <div className="flex items-center space-x-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Popover>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="h-8 gap-1"
                    disabled={loading}
                  >
                    {loading ? (
                      <span className="flex items-center">
                        <Bot className="h-4 w-4 mr-1 animate-pulse" />
                        Loading...
                      </span>
                    ) : activePersona ? (
                      <span className="flex items-center">
                        <Sparkles className="h-4 w-4 mr-1 text-primary" />
                        {activePersona.name}
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <Bot className="h-4 w-4 mr-1" />
                        Select Persona
                      </span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-56 p-3" align="end">
                  <div className="space-y-3">
                    <h4 className="font-medium leading-none">Bot Persona</h4>
                    <p className="text-xs text-muted-foreground">
                      Select a persona for your bot to use
                    </p>
                    <Separator />
                    
                    {personas.length > 0 ? (
                      <PersonaSelector
                        personas={personas}
                        activePersona={activePersona}
                        onSelect={handleSelectPersona}
                        className="w-full"
                      />
                    ) : (
                      <div className="text-center py-2">
                        <p className="text-sm text-muted-foreground mb-2">No personas found</p>
                      </div>
                    )}
                    
                    <div className="flex flex-col gap-2 pt-2">
                      <Link href="/bots/personas" passHref>
                        <Button variant="outline" size="sm" className="w-full">
                          <Settings className="h-4 w-4 mr-2" />
                          Manage Personas
                        </Button>
                      </Link>
                      <Link href="/bots/personas?create=true" passHref>
                        <Button size="sm" className="w-full">
                          <Plus className="h-4 w-4 mr-2" />
                          Create New
                        </Button>
                      </Link>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>Select or manage bot personas</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      
      {/* Error Dialog */}
      <AlertDialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-destructive" />
              Error Loading Personas
            </AlertDialogTitle>
            <AlertDialogDescription>
              {error || "An unexpected error occurred while loading personas."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Dismiss</AlertDialogCancel>
            <AlertDialogAction onClick={fetchPersonas}>Retry</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

