"use client"

import * as React from "react"
import { Bot<PERSON>erson<PERSON> } from "@prisma/client"
import { PersonaCard } from "./persona-card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select"
import { Plus, Search, Filter } from "lucide-react"

// Enhanced BotPersona interface - use the base type directly since it already has the enhanced fields
type EnhancedBotPersona = BotPersona;

// Helper function to safely check if a JsonValue string contains a query
const stringContainsQuery = (value: any, query: string): boolean => {
  if (typeof value === 'string') {
    return value.toLowerCase().includes(query);
  }
  return String(value).toLowerCase().includes(query);
};

interface PersonaGridProps {
  personas: EnhancedBotPersona[]
  activePersonaId?: string
  onSelect?: (persona: EnhancedBotPersona) => void
  onEdit?: (persona: EnhancedBotPersona) => void
  onDelete?: (persona: EnhancedBotPersona) => void
  onExport?: (persona: EnhancedBotPersona) => void
  onCreateNew?: () => void
  onSearch?: (query: string, sort: string, page: number) => Promise<void>
  onSort?: (query: string, sort: string, page: number) => Promise<void>
  onPageChange?: (query: string, sort: string, page: number) => Promise<void>
}

export function PersonaGrid({
  personas,
  activePersonaId,
  onSelect,
  onEdit,
  onDelete,
  onExport,
  onCreateNew,
  onSearch,
  onSort,
  onPageChange,
}: PersonaGridProps) {
  const [searchQuery, setSearchQuery] = React.useState("")
  const [sortBy, setSortBy] = React.useState("name")
  const [isLoading, setIsLoading] = React.useState(false)
  const [page, setPage] = React.useState(1)
  const [totalPages, setTotalPages] = React.useState(1)
  const [currentPersonas, setCurrentPersonas] = React.useState<EnhancedBotPersona[]>(personas)

  // Update current personas when props change
  React.useEffect(() => {
    setCurrentPersonas(personas);
    // Calculate total pages based on the total count from the server
    // This is a fallback in case server-side pagination isn't implemented
    setTotalPages(Math.ceil(personas.length / 10));
  }, [personas]);

  // Handle search and sort changes
  const handleSearch = React.useCallback(async (query: string) => {
    setSearchQuery(query);
    setPage(1); // Reset to first page on new search

    // If onSearch callback is provided, use it for server-side filtering
    if (onSearch) {
      setIsLoading(true);
      try {
        await onSearch(query, sortBy, 1);
      } finally {
        setIsLoading(false);
      }
    }
  }, [onSearch, sortBy]);

  const handleSortChange = React.useCallback(async (sort: string) => {
    setSortBy(sort);

    // If onSort callback is provided, use it for server-side sorting
    if (onSort) {
      setIsLoading(true);
      try {
        await onSort(searchQuery, sort, page);
      } finally {
        setIsLoading(false);
      }
    }
  }, [onSort, searchQuery, page]);

  // Handle pagination
  const handlePageChange = React.useCallback(async (newPage: number) => {
    setPage(newPage);

    // If onPageChange callback is provided, use it for server-side pagination
    if (onPageChange) {
      setIsLoading(true);
      try {
        await onPageChange(searchQuery, sortBy, newPage);
      } finally {
        setIsLoading(false);
      }
    }
  }, [onPageChange, searchQuery, sortBy]);

  // Filter personas based on search query
  const filteredPersonas: EnhancedBotPersona[] = React.useMemo(() => {
    if (!searchQuery.trim()) return currentPersonas

    const query = searchQuery.toLowerCase()
    return currentPersonas.filter((persona: EnhancedBotPersona) => {
      // Search in name
      if (persona.name.toLowerCase().includes(query)) return true

      // Search in description
      if (persona.description?.toLowerCase().includes(query)) return true

      // Search in system prompt
      if (persona.system?.toLowerCase().includes(query)) return true

      // Search in bio
      if (persona.bio && Array.isArray(persona.bio)) {
        if (persona.bio.some(bio => bio !== null && stringContainsQuery(bio, query))) return true
      }

      // Search in topics
      if (persona.topics && Array.isArray(persona.topics)) {
        if (persona.topics.some(topic => topic !== null && stringContainsQuery(topic, query))) return true
      }

      return false
    })
  }, [currentPersonas, searchQuery]);

  // Sort personas
  const sortedPersonas = React.useMemo(() => {
    return [...filteredPersonas].sort((a, b) => {
      // Always show active persona first
      if (a.id === activePersonaId) return -1
      if (b.id === activePersonaId) return 1

      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name)
        case "nameDesc":
          return b.name.localeCompare(a.name)
        case "newest":
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case "oldest":
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        default:
          return 0
      }
    })
  }, [filteredPersonas, sortBy, activePersonaId]);

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-2 justify-between">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search personas..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Select
            value={sortBy}
            onValueChange={handleSortChange}
          >
            <SelectTrigger className="w-[180px]">
              <Filter className="mr-2 h-4 w-4" />
              <span>Sort by</span>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name (A-Z)</SelectItem>
              <SelectItem value="nameDesc">Name (Z-A)</SelectItem>
              <SelectItem value="newest">Newest first</SelectItem>
              <SelectItem value="oldest">Oldest first</SelectItem>
            </SelectContent>
          </Select>
          {onCreateNew && (
            <Button onClick={onCreateNew}>
              <Plus className="mr-2 h-4 w-4" />
              New Persona
            </Button>
          )}
        </div>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 py-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <div
              key={`skeleton-${i}`}
              className="border rounded-lg p-4 h-40 animate-pulse bg-muted"
            />
          ))}
        </div>
      ) : sortedPersonas.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-muted-foreground">No personas found</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 py-4">
          {sortedPersonas.map((persona) => (
            <PersonaCard
              key={persona.id}
              persona={persona}
              isActive={persona.id === activePersonaId}
              onSelect={onSelect}
              onEdit={onEdit}
              onDelete={onDelete}
              onExport={onExport}
            />
          ))}
        </div>
      )}

      {/* Pagination controls */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2 mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(page - 1)}
            disabled={page === 1 || isLoading}
          >
            Previous
          </Button>
          <div className="flex items-center px-2">
            Page {page} of {totalPages}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(page + 1)}
            disabled={page >= totalPages || isLoading}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
