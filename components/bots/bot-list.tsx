"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { <PERSON><PERSON><PERSON>, <PERSON>ci<PERSON>, Trash2, MessageSquare, BrainCircuit, Loader2, AlertCircle } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface BotPersona {
  id: string;
  name: string;
  description?: string;
  personaContent: {
    name: string;
    description?: string;
    tone: string;
    writingStyle: string;
    vocabulary: string;
    traits: string[];
    knowledge: string[];
    rules: string[];
    contextLength: number;
  };
  personaType: string;
  aiProvider: string;
  chatModel?: string;
  embeddingModel?: string;
  createdAt: string;
  updatedAt: string;
}

export function BotList() {
  const [bots, setBots] = useState<BotPersona[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [botToDelete, setBotToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchBots = useCallback(async () => {
    try {
      const response = await fetch('/api/bots');
      if (!response.ok) {
        throw new Error('Failed to fetch bots');
      }
      const data = await response.json();
      setBots(data.bots);
    } catch (error) {
      toast.error('Failed to load bot personas');
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchBots();
  }, [fetchBots]);

  const handleDeleteBot = async () => {
    if (!botToDelete) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/bots/${botToDelete}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete bot');
      }

      setBots(bots.filter(bot => bot.id !== botToDelete));
      setBotToDelete(null);

      toast.success('Bot persona deleted successfully');
    } catch (error) {
      toast.error('Failed to delete bot persona');
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getProviderBadgeColor = (provider: string) => {
    switch (provider) {
      case 'OPENAI':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'GOOGLE':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2 text-muted-foreground">Loading bot personas...</span>
      </div>
    );
  }

  if (bots.length === 0) {
    return (
      <div className="text-center py-12">
        <Sparkles className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">No bot personas yet</h3>
        <p className="mt-2 text-muted-foreground">
          Create your first AI bot persona to get started with automated content generation.
        </p>
        <div className="mt-6">
          <Button asChild>
            <Link href="/dashboard/bots/create">
              <Sparkles className="mr-2 h-4 w-4" />
              Create Your First Bot
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {bots.map((bot) => (
          <Card key={bot.id} className="overflow-hidden transform transition-all hover:shadow-md">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <Avatar className="h-12 w-12 mb-2">
                  <AvatarImage src={`https://avatar.vercel.sh/${bot.id}.png`} alt={bot.name} />
                  <AvatarFallback>
                    <Sparkles className="h-6 w-6" />
                  </AvatarFallback>
                </Avatar>
                <div className="space-x-1">
                  <Button variant="ghost" size="icon" asChild>
                    <Link href={`/dashboard/bots/edit/${bot.id}`}>
                      <Pencil className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setBotToDelete(bot.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">Delete</span>
                  </Button>
                </div>
              </div>
              <CardTitle className="line-clamp-1">{bot.name}</CardTitle>
              <CardDescription className="line-clamp-2">
                {bot.description || bot.personaContent.description || 'No description provided'}
              </CardDescription>
            </CardHeader>
            <CardContent className="pb-4">
              <div className="space-y-3">
                {/* AI Provider Badge */}
                <div className="flex items-center gap-2">
                  <Badge className={getProviderBadgeColor(bot.aiProvider)}>
                    {bot.aiProvider}
                  </Badge>
                  {bot.chatModel && (
                    <Badge variant="outline" className="text-xs">
                      {bot.chatModel}
                    </Badge>
                  )}
                </div>

                {/* Personality Traits */}
                <div className="flex flex-wrap gap-1">
                  {bot.personaContent.traits.slice(0, 3).map((trait) => (
                    <Badge key={trait} variant="secondary" className="text-xs">
                      {trait}
                    </Badge>
                  ))}
                  {bot.personaContent.traits.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{bot.personaContent.traits.length - 3} more
                    </Badge>
                  )}
                </div>

                {/* Metadata */}
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <BrainCircuit className="mr-1 h-4 w-4" />
                    <span>{bot.personaContent.tone}</span>
                  </div>
                  <div>
                    {formatDate(bot.createdAt)}
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t bg-muted/50 px-6 py-3">
              <div className="flex gap-3 w-full">
                <Button variant="outline" className="flex-1" asChild>
                  <Link href={`/dashboard/bots/chat/${bot.id}`}>
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Chat
                  </Link>
                </Button>
                <Button className="flex-1" asChild>
                  <Link href={`/dashboard/publish/editor?bot=${bot.id}`}>
                    <BrainCircuit className="mr-2 h-4 w-4" />
                    Generate
                  </Link>
                </Button>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>

      <AlertDialog open={!!botToDelete} onOpenChange={() => setBotToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this bot persona and all associated data.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteBot}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
