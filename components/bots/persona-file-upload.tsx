"use client"

import * as React from "react"
import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Upload, File, AlertCircle, Check } from "lucide-react"
import { PersonaImportResult } from "@/lib/bots/types"

interface PersonaFileUploadProps {
  onImport: (jsonData: string) => Promise<PersonaImportResult>
  onSuccess?: (result: PersonaImportResult) => void
  className?: string
}

export function PersonaFileUpload({
  onImport,
  onSuccess,
  className
}: PersonaFileUploadProps) {
  // State
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  // Handle drag events
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }
  
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
  }
  
  // Handle file drop
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFile(files[0])
    }
  }
  
  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFile(files[0])
    }
  }
  
  // Handle file processing
  const handleFile = (file: File) => {
    // Reset state
    setError(null)
    setSuccess(false)
    
    // Check file type
    if (!file.name.endsWith('.json')) {
      setError("Please upload a JSON file")
      return
    }
    
    // Read file
    const reader = new FileReader()
    
    reader.onloadstart = () => {
      setIsUploading(true)
      setProgress(0)
    }
    
    reader.onprogress = (event) => {
      if (event.lengthComputable) {
        const percentComplete = Math.round((event.loaded / event.total) * 100)
        setProgress(percentComplete)
      }
    }
    
    reader.onload = async (event) => {
      try {
        const jsonData = event.target?.result as string
        
        // Simulate network delay for better UX
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // Import persona
        const result = await onImport(jsonData)
        
        if (!result.success) {
          throw new Error(result.error || "Failed to import persona")
        }
        
        // Success
        setSuccess(true)
        setIsUploading(false)
        
        // Call success callback
        if (onSuccess) {
          onSuccess(result)
        }
      } catch (error) {
        console.error("Error importing persona:", error)
        setError(error instanceof Error ? error.message : "An unexpected error occurred")
        setIsUploading(false)
      }
    }
    
    reader.onerror = () => {
      setError("Error reading file")
      setIsUploading(false)
    }
    
    reader.readAsText(file)
  }
  
  // Handle button click
  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }
  
  return (
    <div className={className}>
      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileSelect}
        accept=".json"
        className="hidden"
      />
      
      {/* Drop zone */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragging ? "border-primary bg-primary/5" : "border-muted-foreground/25"
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center space-y-4">
          {success ? (
            <>
              <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                <Check className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-lg font-medium text-green-600">Import Successful</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Persona has been imported successfully
                </p>
              </div>
              <Button onClick={() => {
                setSuccess(false)
                if (fileInputRef.current) {
                  fileInputRef.current.value = ""
                }
              }}>
                Import Another
              </Button>
            </>
          ) : isUploading ? (
            <>
              <File className="h-12 w-12 text-muted-foreground/70" />
              <div>
                <p className="text-lg font-medium">Importing Persona...</p>
                <div className="w-full mt-2">
                  <Progress value={progress} className="h-2" />
                </div>
              </div>
            </>
          ) : (
            <>
              <Upload className="h-12 w-12 text-muted-foreground/70" />
              <div>
                <p className="text-lg font-medium">Drag & Drop Persona File</p>
                <p className="text-sm text-muted-foreground mt-1">
                  or click to browse for a JSON file
                </p>
              </div>
              <Button onClick={handleButtonClick}>
                Select File
              </Button>
            </>
          )}
        </div>
      </div>
      
      {/* Error message */}
      {error && (
        <Alert variant="destructive" className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}

