"use client";

import { useState } from "react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import { MoreH<PERSON>zontal, Repeat2, Heart, MessageSquare, Bar<PERSON>hart } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Define types for tweet data
interface TweetStats {
  likes: number;
  retweets: number;
  replies: number;
  impressions: number;
}

interface PublishedTweet {
  id: string;
  content: string;
  publishedAt: Date;
  stats: TweetStats;
}

interface ScheduledTweet {
  id: string;
  content: string;
  scheduledFor: Date;
}

type Tweet = PublishedTweet | ScheduledTweet;

const mockTweets: PublishedTweet[] = [
  {
    id: "1",
    content: "Just released our new AI-powered content generation feature! Create engaging tweets in seconds with customizable personalities. #AI #ContentCreation",
    publishedAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    stats: { likes: 42, retweets: 12, replies: 7, impressions: 1250 }
  },
  {
    id: "2",
    content: "Pro tip: Scheduling your tweets at optimal times can increase engagement by up to 25%. What's your favorite time to post? #SocialMediaTips",
    publishedAt: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
    stats: { likes: 38, retweets: 15, replies: 23, impressions: 2100 }
  },
  {
    id: "3",
    content: "Our knowledge base feature now supports semantic search! Find exactly what you need with natural language queries. #ProductUpdate",
    publishedAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    stats: { likes: 56, retweets: 18, replies: 4, impressions: 1850 }
  }
];

const scheduledTweets: ScheduledTweet[] = [
  {
    id: "4",
    content: "Coming soon: Advanced analytics to track your tweet performance and audience engagement. Stay tuned! #Analytics",
    scheduledFor: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2) // 2 days from now
  },
  {
    id: "5",
    content: "Join our webinar next week on 'Maximizing Social Media Impact with AI'. Register now at example.com/webinar #AIWebinar",
    scheduledFor: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5) // 5 days from now
  }
];

function TweetCard({ tweet, scheduled = false }: { tweet: Tweet, scheduled?: boolean }) {
  // Type guard to check if the tweet is a PublishedTweet
  const isPublishedTweet = (tweet: Tweet): tweet is PublishedTweet => 
    'publishedAt' in tweet && 'stats' in tweet;

  return (
    <div className="border-b pb-4 last:border-0 last:pb-0">
      <div className="flex items-start justify-between gap-4 pt-4">
        <div className="flex items-start gap-4">
          <Avatar>
            <AvatarImage src="/assets/avatar.png" alt="xtasker" />
            <AvatarFallback>XT</AvatarFallback>
          </Avatar>
          <div className="grid gap-1">
            <div className="flex items-center gap-2">
              <div className="font-semibold">xtasker</div>
              <div className="text-sm text-muted-foreground">@xtasker</div>
              <div className="text-sm text-muted-foreground">·</div>
              <div className="text-sm text-muted-foreground">
                {scheduled ? 
                  `Scheduled for ${(tweet as ScheduledTweet).scheduledFor.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}` : 
                  formatDistanceToNow((tweet as PublishedTweet).publishedAt, { addSuffix: true })}
              </div>
            </div>
            <div className="text-sm">
              {tweet.content}
            </div>
            {!scheduled && isPublishedTweet(tweet) && (
              <div className="flex items-center gap-4 pt-2 text-muted-foreground">
                <div className="flex items-center gap-1 text-xs">
                  <MessageSquare className="h-3.5 w-3.5" />
                  {tweet.stats.replies}
                </div>
                <div className="flex items-center gap-1 text-xs">
                  <Repeat2 className="h-3.5 w-3.5" />
                  {tweet.stats.retweets}
                </div>
                <div className="flex items-center gap-1 text-xs">
                  <Heart className="h-3.5 w-3.5" />
                  {tweet.stats.likes}
                </div>
                <div className="flex items-center gap-1 text-xs">
                  <BarChart className="h-3.5 w-3.5" />
                  {tweet.stats.impressions}
                </div>
              </div>
            )}
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View details</DropdownMenuItem>
            {scheduled ? (
              <>
                <DropdownMenuItem>Edit</DropdownMenuItem>
                <DropdownMenuItem>Publish now</DropdownMenuItem>
              </>
            ) : (
              <DropdownMenuItem>Duplicate</DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-destructive">
              {scheduled ? 'Delete schedule' : 'Delete tweet'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}

export function RecentTweetsCard() {
  return (
    <Card className="col-span-1">
      <CardHeader className="pb-3">
        <CardTitle>Recent Tweets</CardTitle>
        <CardDescription>
          Your latest published and scheduled tweets
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="published" className="px-6">
          <TabsList className="mb-4 w-full">
            <TabsTrigger value="published" className="flex-1">Published</TabsTrigger>
            <TabsTrigger value="scheduled" className="flex-1">Scheduled</TabsTrigger>
          </TabsList>
          <TabsContent value="published" className="pb-4">
            <div className="space-y-0">
              {mockTweets.map((tweet) => (
                <TweetCard key={tweet.id} tweet={tweet} />
              ))}
            </div>
          </TabsContent>
          <TabsContent value="scheduled" className="pb-4">
            <div className="space-y-0">
              {scheduledTweets.map((tweet) => (
                <TweetCard key={tweet.id} tweet={tweet} scheduled={true} />
              ))}
            </div>
          </TabsContent>
        </Tabs>
        <div className="flex justify-center pb-4 pt-2">
          <Link href="/dashboard/schedule/list">
            <Button variant="outline" size="sm">View All</Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}