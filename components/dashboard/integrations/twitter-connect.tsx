"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { toast } from 'sonner';
import { signIn, useSession } from "next-auth/react";
import useSWR from 'swr';

interface LinkedTwitterAccount {
  id: string;
  providerAccountId: string;
  name: string | null;
  screenName: string | null;
  profilePicture: string | null;
}

export function TwitterConnect() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);

  // Fetch linked accounts from our own API
  const { data: linkedAccounts, error, mutate } = useSWR<LinkedTwitterAccount[]>(
    session ? '/api/linked-accounts/twitter' : null,
    (url: string) => fetch(url).then(res => res.json())
  );
  
  const handleConnect = async () => {
    setIsLoading(true);
    await signIn('twitter');
    setIsLoading(false);
  };

  const handleDisconnect = async (accountId: string) => {
    const toastId = toast.loading('Disconnecting Twitter account...');
    try {
      const response = await fetch(`/api/linked-accounts/${accountId}`, { method: 'DELETE' });
      if (response.ok) {
        toast.success('Account disconnected successfully.', { id: toastId });
        mutate(); // Re-fetch data
      } else {
        const data = await response.json();
        toast.error(data.error || 'Failed to disconnect account.', { id: toastId });
      }
    } catch (error) {
      toast.error('An unexpected error occurred.', { id: toastId });
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-4">
          <Image src="/logos/twitter.svg" alt="Twitter" width={40} height={40} />
          <div>
            <CardTitle>Twitter</CardTitle>
            <CardDescription>Connect your X/Twitter account to schedule and publish tweets.</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading || !linkedAccounts && !error ? (
          <p>Loading...</p>
        ) : linkedAccounts && linkedAccounts.length > 0 ? (
          <div className="space-y-4">
            {linkedAccounts.map(account => (
              <div key={account.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {account.profilePicture && (
                    <Image src={account.profilePicture} alt={account.name || 'Twitter avatar'} width={40} height={40} className="rounded-full" />
                  )}
                  <div>
                    <p className="font-semibold">{account.name}</p>
                    <p className="text-sm text-muted-foreground">@{account.screenName || account.providerAccountId}</p>
                  </div>
                </div>
                <Button variant="destructive" size="sm" onClick={() => handleDisconnect(account.id)}>Disconnect</Button>
              </div>
            ))}
          </div>
        ) : (
          <Button onClick={handleConnect} disabled={isLoading}>
            {isLoading ? "Connecting..." : "Connect Twitter"}
          </Button>
        )}
      </CardContent>
    </Card>
  );
} 