"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { toast } from 'sonner';
import { signIn, useSession } from "next-auth/react";
import useSWR from 'swr';

interface LinkedGoogleAccount {
  id: string;
  providerAccountId: string;
  name: string | null;
  profilePicture: string | null;
}

export function GoogleConnect() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);

  // Fetch linked accounts from our own API
  const { data: linkedAccounts, error, mutate } = useSWR<LinkedGoogleAccount[]>(
    session ? '/api/linked-accounts/google' : null,
    (url: string) => fetch(url).then(res => res.json())
  );
  
  const handleConnect = async () => {
    setIsLoading(true);
    // The page will be redirected to Google for auth, so no need to setIsLoading(false) on success
    await signIn('google'); 
  };

  const handleDisconnect = async (accountId: string) => {
    const toastId = toast.loading('Disconnecting Google account...');
    try {
      const response = await fetch(`/api/linked-accounts/${accountId}`, { method: 'DELETE' });
      if (response.ok) {
        toast.success('Account disconnected successfully.', { id: toastId });
        mutate(); // Re-fetch data
      } else {
        const data = await response.json();
        toast.error(data.error || 'Failed to disconnect account.', { id: toastId });
      }
    } catch (error) {
      toast.error('An unexpected error occurred.', { id: toastId });
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-4">
          <Image src="/logos/google.svg" alt="Google" width={40} height={40} />
          <div>
            <CardTitle>Google</CardTitle>
            <CardDescription>Connect your Google account for future integrations.</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading || !linkedAccounts && !error ? (
          <p>Loading...</p>
        ) : linkedAccounts && linkedAccounts.length > 0 ? (
          <div className="space-y-4">
            {linkedAccounts.map(account => (
              <div key={account.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {account.profilePicture && (
                    <Image src={account.profilePicture} alt={account.name || 'Google avatar'} width={40} height={40} className="rounded-full" />
                  )}
                  <div>
                    <p className="font-semibold">{account.name}</p>
                    {/* Google doesn't have a screenName, we can use the providerAccountId (email) if needed */}
                  </div>
                </div>
                <Button variant="destructive" size="sm" onClick={() => handleDisconnect(account.id)}>Disconnect</Button>
              </div>
            ))}
          </div>
        ) : (
          <Button onClick={handleConnect} disabled={isLoading}>
            {isLoading ? "Connecting..." : "Connect Google"}
          </Button>
        )}
      </CardContent>
    </Card>
  );
} 