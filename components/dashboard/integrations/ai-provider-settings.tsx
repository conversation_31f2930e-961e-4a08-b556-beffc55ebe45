"use client";

import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

export function AiProviderSettings() {
  const [openAiKey, setOpenAiKey] = useState('');
  const [googleApiKey, setGoogleApiKey] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Fetch existing keys on component mount
    async function fetchKeys() {
      try {
        const res = await fetch('/api/ai/config/keys');
        if (res.ok) {
          const data = await res.json();
          setOpenAiKey(data.openai || '');
          setGoogleApiKey(data.google || '');
        }
      } catch (error) {
        console.error("Failed to fetch API keys:", error);
      }
    }
    fetchKeys();
  }, []);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const res = await fetch('/api/ai/config/keys', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ openai: openAiKey, google: googleApiKey }),
      });

      if (res.ok) {
        toast.success("API keys saved successfully.");
      } else {
        const errorData = await res.json();
        toast.error(errorData.error || "Failed to save API keys.");
      }
    } catch (error) {
      toast.error("An unexpected error occurred.");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card className="col-span-1 md:col-span-2">
      <CardHeader>
        <CardTitle>AI Provider API Keys</CardTitle>
        <CardDescription>
          Manage your API keys for OpenAI and Google AI. These are required to use AI-powered features.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="openai-key" className="text-sm font-medium">OpenAI API Key</label>
          <Input 
            id="openai-key" 
            type="password"
            value={openAiKey}
            onChange={(e) => setOpenAiKey(e.target.value)}
            placeholder="sk-..."
          />
        </div>
        <div className="space-y-2">
          <label htmlFor="google-key" className="text-sm font-medium">Google AI API Key</label>
          <Input 
            id="google-key"
            type="password"
            value={googleApiKey}
            onChange={(e) => setGoogleApiKey(e.target.value)}
            placeholder="AIza..."
          />
        </div>
        <Button onClick={handleSave} disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save Keys'}
        </Button>
      </CardContent>
    </Card>
  );
} 