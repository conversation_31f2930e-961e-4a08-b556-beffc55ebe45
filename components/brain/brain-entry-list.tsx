"use client";

import { useState } from "react";
import Link from "next/link";
import { Edit, Trash2, <PERSON><PERSON><PERSON>, ExternalLink, MoreVertical } from "lucide-react";
import { format } from "date-fns";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { truncateText } from "@/lib/utils";

const mockEntries = [
  {
    id: "1",
    title: "X Best Practices",
    content: "1. Post consistently\n2. Use relevant hashtags\n3. Include visual media\n4. Engage with your audience\n5. Analyze performance metrics",
    sourceUrl: "https://blog.twitter.com/best-practices",
    category: { id: "marketing", name: "Marketing" },
    createdAt: new Date("2025-03-15T10:30:00"),
    updatedAt: new Date("2025-03-15T14:22:00"),
  },
  {
    id: "2",
    title: "Company Product Launch Announcement",
    content: "We're excited to announce our newest product, featuring advanced AI capabilities and intuitive design. This groundbreaking solution will transform how you manage your workflow. The new features include automated content generation, smart scheduling, and advanced analytics that help you understand your audience better.",
    category: { id: "product", name: "Product" },
    createdAt: new Date("2025-03-10T09:15:00"),
    updatedAt: new Date("2025-03-12T11:45:00"),
  },
  {
    id: "3",
    title: "Industry Trends 2025",
    content: "Key trends for 2025 include:\n- AI-powered automation\n- Remote work optimization\n- Blockchain for content verification\n- Voice-first interfaces\n- Privacy-focused technologies\n\nThese trends are reshaping how we think about digital communication and content creation.",
    sourceUrl: "https://techcrunch.com/2025-trends",
    category: { id: "industry", name: "Industry News" },
    createdAt: new Date("2025-02-28T14:20:00"),
    updatedAt: new Date("2025-03-05T16:30:00"),
  },
  {
    id: "4",
    title: "Competitor Analysis: TweetBuddy",
    content: "TweetBuddy recently launched their AI writing assistant with the following features:\n- Basic text generation\n- Limited scheduling\n- No knowledge base\n- Pricing: $19/month",
    category: { id: "competitors", name: "Competitors" },
    createdAt: new Date("2025-03-02T11:45:00"),
    updatedAt: new Date("2025-03-02T11:45:00"),
  },
  {
    id: "5",
    title: "Content Ideas",
    content: "Quick content ideas for social media:\n• Behind-the-scenes content\n• User-generated content\n• Educational tips\n• Industry news commentary",
    category: { id: "marketing", name: "Marketing" },
    createdAt: new Date("2025-03-01T08:00:00"),
    updatedAt: new Date("2025-03-01T08:00:00"),
  },
  {
    id: "6",
    title: "AI Writing Prompts",
    content: "Effective prompts for AI content generation: Be specific about tone, audience, and desired outcome. Include context about your brand voice and target demographics.",
    category: { id: "product", name: "Product" },
    createdAt: new Date("2025-02-25T16:30:00"),
    updatedAt: new Date("2025-02-25T16:30:00"),
  },
];

export function BrainEntryList() {
  const [entries, setEntries] = useState(mockEntries);
  const [entryToDelete, setEntryToDelete] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleDeleteEntry = () => {
    if (entryToDelete) {
      setEntries(entries.filter(entry => entry.id !== entryToDelete));
      setEntryToDelete(null);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Knowledge Entries</h2>
            <p className="text-muted-foreground">
              Browse and manage your knowledge base content
            </p>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="h-fit">
              <CardHeader>
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
              <CardFooter>
                <Skeleton className="h-8 w-20" />
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Knowledge Entries</h2>
          <p className="text-muted-foreground">
            Browse and manage your knowledge base content
          </p>
        </div>
        <div className="text-sm text-muted-foreground">
          {entries.length} {entries.length === 1 ? 'entry' : 'entries'}
        </div>
      </div>

      {entries.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <BookOpen className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No entries yet</h3>
            <p className="text-muted-foreground text-center mb-4">
              Start building your knowledge base by creating your first entry.
            </p>
            <Button asChild>
              <Link href="/dashboard/brain/create">
                Create Entry
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {entries.map((entry) => (
            <Card key={entry.id} className="h-fit hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {entry.title && (
                      <CardTitle className="text-lg line-clamp-2 mb-2">
                        {entry.title}
                      </CardTitle>
                    )}
                    <div className="flex items-center gap-2">
                      {entry.category && (
                        <Badge variant="outline" className="text-xs">
                          {entry.category.name}
                        </Badge>
                      )}
                      <span className="text-xs text-muted-foreground">
                        {format(entry.updatedAt, 'MMM d, yyyy')}
                      </span>
                    </div>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/brain/view/${entry.id}`}>
                          <BookOpen className="mr-2 h-4 w-4" />
                          View
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/brain/edit/${entry.id}`}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      {entry.sourceUrl && (
                        <DropdownMenuItem asChild>
                          <a href={entry.sourceUrl} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Source
                          </a>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => setEntryToDelete(entry.id)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground line-clamp-4">
                  {entry.content}
                </p>
              </CardContent>

              <CardFooter className="pt-0">
                <Button variant="outline" size="sm" asChild className="w-full">
                  <Link href={`/dashboard/brain/view/${entry.id}`}>
                    <BookOpen className="mr-2 h-4 w-4" />
                    Read More
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      <AlertDialog open={!!entryToDelete} onOpenChange={() => setEntryToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this knowledge base entry.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteEntry}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}