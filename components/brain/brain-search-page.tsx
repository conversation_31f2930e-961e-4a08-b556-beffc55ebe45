"use client";

import { useState } from "react";
import { Search, Sparkles, BookOpen, ExternalLink } from "lucide-react";
import { format } from "date-fns";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface SearchResult {
  entry: {
    id: string;
    title?: string;
    content: string;
    sourceUrl?: string;
    categoryId?: string;
    createdAt: Date;
    updatedAt: Date;
  };
  category?: {
    id: string;
    name: string;
  };
  similarity: number;
}

const mockCategories = [
  { id: "all", name: "All Categories" },
  { id: "marketing", name: "Marketing" },
  { id: "product", name: "Product" },
  { id: "industry", name: "Industry News" },
  { id: "competitors", name: "Competitors" },
];

const mockSearchResults: SearchResult[] = [
  {
    entry: {
      id: "1",
      title: "X Best Practices",
      content: "1. Post consistently\n2. Use relevant hashtags\n3. Include visual media\n4. Engage with your audience\n5. Analyze performance metrics",
      categoryId: "marketing",
      createdAt: new Date("2025-03-15T10:30:00"),
      updatedAt: new Date("2025-03-15T14:22:00"),
    },
    category: { id: "marketing", name: "Marketing" },
    similarity: 0.92,
  },
  {
    entry: {
      id: "2",
      title: "Company Product Launch Announcement",
      content: "We're excited to announce our newest product, featuring advanced AI capabilities and intuitive design. This groundbreaking solution will transform how you manage your workflow.",
      categoryId: "product",
      createdAt: new Date("2025-03-10T09:15:00"),
      updatedAt: new Date("2025-03-12T11:45:00"),
    },
    category: { id: "product", name: "Product" },
    similarity: 0.78,
  },
];

export function BrainSearchPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setHasSearched(true);

    try {
      // TODO: Replace with actual API call
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock search results
      setSearchResults(mockSearchResults);
    } catch (error) {
      console.error("Search failed:", error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  return (
    <div className="space-y-6">
      {/* Search Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            AI-Powered Semantic Search
          </CardTitle>
          <CardDescription>
            Search your knowledge base using natural language. Find relevant content based on meaning, not just keywords.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex gap-2">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Describe what you're looking for... (e.g., 'social media best practices', 'product launch ideas')"
                    className="pl-9"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              
              <Select
                value={selectedCategory}
                onValueChange={setSelectedCategory}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {mockCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Button type="submit" disabled={!searchQuery.trim() || isSearching}>
                {isSearching ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="mr-2 h-4 w-4" />
                    Search
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Search Results */}
      {hasSearched && (
        <Card>
          <CardHeader>
            <CardTitle>Search Results</CardTitle>
            <CardDescription>
              {isSearching 
                ? "Searching your knowledge base..." 
                : `Found ${searchResults.length} relevant entries for "${searchQuery}"`
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isSearching ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Analyzing your knowledge base...
                  </p>
                </div>
              </div>
            ) : searchResults.length === 0 ? (
              <div className="text-center py-8">
                <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No results found</h3>
                <p className="text-muted-foreground">
                  Try adjusting your search query or check a different category.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {searchResults.map((result, index) => (
                  <div key={result.entry.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {result.entry.title && (
                            <h3 className="font-medium">{result.entry.title}</h3>
                          )}
                          {result.category && (
                            <Badge variant="outline">{result.category.name}</Badge>
                          )}
                          <Badge variant="secondary" className="ml-auto">
                            {Math.round(result.similarity * 100)}% match
                          </Badge>
                        </div>
                        
                        <p className="text-sm text-muted-foreground mb-3">
                          {truncateText(result.entry.content, 200)}
                        </p>
                        
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>
                            Updated {format(result.entry.updatedAt, "MMM d, yyyy")}
                          </span>
                          {result.entry.sourceUrl && (
                            <a
                              href={result.entry.sourceUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 hover:text-primary"
                            >
                              <ExternalLink className="h-3 w-3" />
                              Source
                            </a>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex gap-2 ml-4">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/dashboard/brain/view/${result.entry.id}`}>
                            <BookOpen className="h-4 w-4 mr-1" />
                            View
                          </Link>
                        </Button>
                      </div>
                    </div>
                    
                    {index < searchResults.length - 1 && <Separator />}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Search Tips */}
      {!hasSearched && (
        <Card>
          <CardHeader>
            <CardTitle>Search Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div>
                <strong>Natural Language:</strong> Use complete sentences like &quot;How to improve social media engagement&quot;
              </div>
              <div>
                <strong>Concepts:</strong> Search for concepts and ideas, not just exact keywords
              </div>
              <div>
                <strong>Context:</strong> The AI understands context and relationships between ideas
              </div>
              <div>
                <strong>Categories:</strong> Filter by category to narrow down results
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
