"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>Left, Save, Sparkles } from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";

interface Category {
  id: string;
  name: string;
}

interface BrainEntry {
  id: string;
  title?: string;
  content: string;
  sourceUrl?: string;
  categoryId?: string;
}

interface BrainEntryFormProps {
  entryId?: string;
}

// Mock categories
const mockCategories: Category[] = [
  { id: "marketing", name: "Marketing" },
  { id: "product", name: "Product" },
  { id: "industry", name: "Industry News" },
  { id: "competitors", name: "Competitors" },
];

export function BrainEntryForm({ entryId }: BrainEntryFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingEntry, setIsLoadingEntry] = useState(!!entryId);

  const [formData, setFormData] = useState({
    title: "",
    content: "",
    sourceUrl: "",
    categoryId: "",
  });

  const isEditing = !!entryId;

  // Load entry data if editing
  useEffect(() => {
    if (entryId) {
      // TODO: Replace with actual API call
      setIsLoadingEntry(true);
      setTimeout(() => {
        // Mock data for editing
        setFormData({
          title: "Sample Entry Title",
          content: "This is sample content for editing...",
          sourceUrl: "https://example.com",
          categoryId: "marketing",
        });
        setIsLoadingEntry(false);
      }, 500);
    }
  }, [entryId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.content.trim()) {
      toast.error("Please enter some content for the entry.");
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Replace with actual API call
      if (isEditing) {
        // Update existing entry
        console.log("Updating entry:", entryId, formData);
      } else {
        // Create new entry
        console.log("Creating entry:", formData);
      }

      toast.success(`Your knowledge base entry has been ${isEditing ? "updated" : "created"} successfully.`);

      router.push("/dashboard/brain/view");
    } catch (error) {
      toast.error(`Failed to ${isEditing ? "update" : "create"} entry. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateWithAI = async () => {
    if (!formData.sourceUrl) {
      toast.error("Please enter a URL to generate content from.");
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Implement AI content generation from URL
      toast("AI content generation will be implemented soon.");
    } catch (error) {
      toast.error("Failed to generate content. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingEntry) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-sm text-muted-foreground">Loading entry...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/dashboard/brain/view">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h3 className="text-lg font-medium">
            {isEditing ? "Edit Entry" : "Create New Entry"}
          </h3>
          <p className="text-sm text-muted-foreground">
            {isEditing ? "Update your knowledge base entry" : "Add new content to your knowledge base"}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{isEditing ? "Edit Entry" : "New Knowledge Entry"}</CardTitle>
          <CardDescription>
            {isEditing
              ? "Update the details of your knowledge base entry"
              : "Fill in the details to create a new knowledge base entry"
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="title">Title (Optional)</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Enter a title for this entry..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.categoryId}
                  onValueChange={(value) => setFormData({ ...formData, categoryId: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No category</SelectItem>
                    {mockCategories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="sourceUrl">Source URL (Optional)</Label>
              <div className="flex gap-2">
                <Input
                  id="sourceUrl"
                  type="url"
                  value={formData.sourceUrl}
                  onChange={(e) => setFormData({ ...formData, sourceUrl: e.target.value })}
                  placeholder="https://example.com/article"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleGenerateWithAI}
                  disabled={!formData.sourceUrl || isLoading}
                >
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generate
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Add a source URL and use AI to generate content summary
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Content *</Label>
              <Textarea
                id="content"
                value={formData.content}
                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                placeholder="Enter your content here... This will be used for AI-powered search and recommendations."
                rows={10}
                required
              />
              <p className="text-xs text-muted-foreground">
                This content will be processed for semantic search. The more detailed, the better the search results.
              </p>
            </div>

            <div className="flex justify-end gap-2">
              <Link href="/dashboard/brain/view">
                <Button variant="outline" type="button">
                  Cancel
                </Button>
              </Link>
              <Button type="submit" disabled={isLoading || !formData.content.trim()}>
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                    {isEditing ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {isEditing ? "Update Entry" : "Create Entry"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
