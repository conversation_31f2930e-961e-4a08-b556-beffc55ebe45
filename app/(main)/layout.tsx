import { <PERSON>ada<PERSON> } from 'next';
import { redirect } from 'next/navigation';

import { MainNav, MobileNav } from '@/components/layout/main-nav';
import { UserAccountNav } from '@/components/layout/user-account-nav';
import { PersonaHeaderSelector } from '@/components/bots/persona-header-selector';
import { cookies } from 'next/headers';
import { config } from '@/lib/config';
import { UserJwtPayload } from '@/lib/types/auth';

// Simple JWT verification for server components
async function verifyJWT(token: string, secret: string): Promise<UserJwtPayload | null> {
  try {
    const [header, payload, signature] = token.split('.');
    if (!header || !payload || !signature) {
      return null;
    }

    // Decode payload
    const decodedPayload = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')));

    // Check expiration
    if (decodedPayload.exp && Date.now() >= decodedPayload.exp * 1000) {
      return null;
    }

    return decodedPayload as UserJwtPayload;
  } catch (error) {
    return null;
  }
}

// Force dynamic rendering for this layout
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export const metadata: Metadata = {
  title: 'xtasker - Dashboard',
  description: 'AI-Powered Social Media Management',
};

async function getUser() {
  const cookieStore = await cookies();
  const token = cookieStore.get(config.auth.session.cookieName)?.value;

  if (!token) {
    return null;
  }

  try {
    return await verifyJWT(token, config.auth.jwt.secret);
  } catch (error) {
    return null;
  }
}

export default async function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await getUser();

  if (!user) {
    redirect('/login');
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-14 items-center px-4 md:px-6">
          <div className="flex items-center gap-2 font-semibold md:mr-4">
            <span className="text-lg font-semibold hidden md:inline-flex">xtasker</span>
          </div>
          <MainNav />
          <div className="ml-auto flex items-center space-x-4">
            <PersonaHeaderSelector />
            <UserAccountNav
              user={{
                id: user.id,
                name: user.name,
                email: user.email,
                image: user.image,
              }}
            />
          </div>
        </div>
        <MobileNav />
      </header>
      <main className="flex-1 p-4 md:p-6">{children}</main>
    </div>
  );
}
