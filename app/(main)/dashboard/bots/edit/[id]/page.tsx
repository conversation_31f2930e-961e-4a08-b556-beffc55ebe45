"use client";

import { useState, useEffect, useCallback, use } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { <PERSON>L<PERSON>t, Bot, Loader2, <PERSON><PERSON><PERSON> } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import Link from 'next/link';

const updateBotSchema = z.object({
  name: z.string().min(1, 'Bot name is required').max(50, 'Name must be 50 characters or less'),
  description: z.string().max(500, 'Description must be 500 characters or less').optional(),
  aiProvider: z.enum(['OPENAI', 'GOOGLE'], {
    required_error: 'Please select an AI provider',
  }),
  chatModel: z.string().optional(),
  embeddingModel: z.string().optional(),
  persona: z.object({
    name: z.string().min(1, 'Persona name is required').max(50),
    description: z.string().max(500).optional(),
    tone: z.string().min(1, 'Tone is required').max(100),
    writingStyle: z.string().min(1, 'Writing style is required').max(200),
    vocabulary: z.string().min(1, 'Vocabulary level is required').max(200),
    traits: z.array(z.string()).min(1, 'At least one trait is required').max(10),
    knowledge: z.array(z.string()).min(1, 'At least one knowledge area is required').max(20),
    rules: z.array(z.string()).min(1, 'At least one rule is required').max(10),
    contextLength: z.number().int().min(100).max(2000),
  }).optional(),
});

type UpdateBotFormData = z.infer<typeof updateBotSchema>;

interface BotPersona {
  id: string;
  name: string;
  description?: string;
  personaContent: {
    name: string;
    description?: string;
    tone: string;
    writingStyle: string;
    vocabulary: string;
    traits: string[];
    knowledge: string[];
    rules: string[];
    contextLength: number;
  };
  personaType: string;
  aiProvider: string;
  chatModel?: string;
  embeddingModel?: string;
  createdAt: string;
  updatedAt: string;
}

interface ProviderModels {
  provider: string;
  chat: string[];
  embedding: string[];
  isConfigured: boolean;
  error?: string;
}

export default function EditBotPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [bot, setBot] = useState<BotPersona | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [availableModels, setAvailableModels] = useState<ProviderModels[]>([]);

  const resolvedParams = use(params);

  const form = useForm<UpdateBotFormData>({
    resolver: zodResolver(updateBotSchema),
    defaultValues: {
      name: '',
      description: '',
      aiProvider: 'OPENAI',
      chatModel: '',
      embeddingModel: '',
      persona: {
        name: '',
        description: '',
        tone: 'Professional',
        writingStyle: 'Concise',
        vocabulary: 'Industry-standard',
        traits: [],
        knowledge: [],
        rules: [],
        contextLength: 1000,
      },
    },
  });

  useEffect(() => {
    // Define functions inside useEffect to avoid hoisting issues
    async function loadBot() {
      try {
        const response = await fetch(`/api/bots/${resolvedParams.id}`);
        if (!response.ok) {
          if (response.status === 404) {
            toast.error('Bot not found');
            router.push('/dashboard/bots/view');
            return;
          }
          throw new Error('Failed to fetch bot');
        }
        const data = await response.json();
        setBot(data.bot);

        // Populate form with bot data
        form.reset({
          name: data.bot.name,
          description: data.bot.description || '',
          aiProvider: data.bot.aiProvider,
          chatModel: data.bot.chatModel || '',
          embeddingModel: data.bot.embeddingModel || '',
          persona: data.bot.personaContent,
        });
      } catch (error) {
        toast.error('Failed to load bot data');
        router.push('/dashboard/bots/view');
      } finally {
        setIsLoading(false);
      }
    }

    async function loadModels() {
      try {
        const response = await fetch('/api/ai/config/models');
        if (!response.ok) throw new Error('Failed to fetch models');
        const data = await response.json();
        setAvailableModels(data.providers);
      } catch (error) {
        console.error('Failed to load models:', error);
      }
    }

    loadBot();
    loadModels();
  }, [resolvedParams.id, toast, router, form]);

  const selectedProvider = form.watch('aiProvider');
  const currentProviderModels = availableModels.find(p => p.provider === selectedProvider);

  const onSubmit = async (data: UpdateBotFormData) => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/bots/${resolvedParams.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update bot');
      }

      toast.success('Bot persona updated successfully!');

      router.push('/dashboard/bots/view');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to update bot');
    } finally {
      setIsSubmitting(false);
    }
  };

  const parseCommaSeparatedList = (value: string): string[] => {
    return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2 text-muted-foreground">Loading bot data...</span>
      </div>
    );
  }

  if (!bot) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold">Bot not found</h3>
        <p className="text-muted-foreground">The bot you&apos;re looking for doesn&apos;t exist.</p>
        <Button asChild className="mt-4">
          <Link href="/dashboard/bots/view">Back to Bots</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/dashboard/bots/view">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Edit Bot Persona</h1>
          <p className="text-muted-foreground">
            Update the configuration for &quot;{bot.name}&quot;
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  Basic Information
                </CardTitle>
                <CardDescription>
                  Configure the basic settings for your bot persona
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bot Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Professional Marketer" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Brief description of what this bot does..."
                          className="resize-none"
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="aiProvider"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>AI Provider</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select AI provider" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableModels
                            .filter(p => p.isConfigured)
                            .map(provider => (
                              <SelectItem key={provider.provider} value={provider.provider}>
                                {provider.provider === 'OPENAI' ? 'OpenAI' : 'Google AI'}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {currentProviderModels && (
                  <>
                    <FormField
                      control={form.control}
                      name="chatModel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Chat Model (Optional)</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select chat model" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="">Use default model</SelectItem>
                              {currentProviderModels.chat.map(model => (
                                <SelectItem key={model} value={model}>
                                  {model}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Leave empty to use your default model
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="embeddingModel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Embedding Model (Optional)</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select embedding model" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="">Use default model</SelectItem>
                              {currentProviderModels.embedding.map(model => (
                                <SelectItem key={model} value={model}>
                                  {model}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Leave empty to use your default model
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
              </CardContent>
            </Card>

            {/* Personality Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  Personality Configuration
                </CardTitle>
                <CardDescription>
                  Define the personality and behavior of your bot
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="persona.name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Persona Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Marketing Expert" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="persona.tone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tone</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., professional, friendly, casual" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="persona.writingStyle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Writing Style</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., concise and engaging with clear CTAs" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="persona.vocabulary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Vocabulary Level</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., business professional, casual, technical" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="persona.contextLength"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Context Length</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={100}
                          max={2000}
                          placeholder="1000"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1000)}
                        />
                      </FormControl>
                      <FormDescription>
                        Maximum context length for AI generation (100-2000)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </div>

          {/* Traits, Knowledge, and Rules */}
          <Card>
            <CardHeader>
              <CardTitle>Personality Details</CardTitle>
              <CardDescription>
                Define specific traits, knowledge areas, and rules for your bot (comma-separated)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="persona.traits"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Personality Traits</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="confident, analytical, results-driven, empathetic"
                        className="resize-none"
                        rows={2}
                        value={field.value?.join(', ') || ''}
                        onChange={(e) => field.onChange(parseCommaSeparatedList(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter personality traits separated by commas (max 10)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="persona.knowledge"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Knowledge Areas</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="digital marketing, social media strategy, content creation, analytics"
                        className="resize-none"
                        rows={3}
                        value={field.value?.join(', ') || ''}
                        onChange={(e) => field.onChange(parseCommaSeparatedList(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter knowledge areas separated by commas (max 20)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="persona.rules"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Rules to Follow</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Always include a call to action, Keep tweets under 280 characters, Use relevant hashtags"
                        className="resize-none"
                        rows={3}
                        value={field.value?.join(', ') || ''}
                        onChange={(e) => field.onChange(parseCommaSeparatedList(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter rules for the bot to follow separated by commas (max 10)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" asChild>
              <Link href="/dashboard/bots/view">Cancel</Link>
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating Bot...
                </>
              ) : (
                <>
                  <Bot className="mr-2 h-4 w-4" />
                  Update Bot
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}