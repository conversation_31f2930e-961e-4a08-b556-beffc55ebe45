"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";

export default function PublishPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the editor page
    router.push("/dashboard/publish/editor");
  }, [router]);

  return (
    <div className="flex h-[50vh] w-full flex-col items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
      <p className="mt-4 text-sm text-muted-foreground">
        Redirecting to editor...
      </p>
    </div>
  );
} 