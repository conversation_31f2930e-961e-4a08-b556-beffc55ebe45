"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Calendar, Image, CloudLightning as <PERSON>, Send, <PERSON>rk<PERSON>, Clock } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TweetPreview } from "@/components/tweet/tweet-preview";
import { ScheduleSelector } from "@/components/tweet/schedule-selector";
import { EmojiPicker } from "@/components/tweet/emoji-picker";
import { MediaUploader } from "@/components/tweet/media-uploader";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface UploadedFile {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  fileURL: string;
  createdAt: Date;
}

const formSchema = z.object({
  content: z
    .string()
    .max(280, { message: "Tweet cannot exceed 280 characters" })
    .optional(),
  twitterAccountId: z.string().min(1, { message: "Twitter account is required" }),
  botId: z.string().optional(),
  aiPrompt: z.string().optional(),
  scheduledFor: z.date().optional(),
  media: z.array(z.object({
    id: z.string(),
    fileName: z.string(),
    fileType: z.string(),
    fileSize: z.number(),
    fileURL: z.string(),
    createdAt: z.date(),
  })).optional(),
});

const mockBots = [
  { id: "1", name: "Professional Marketer" },
  { id: "2", name: "Casual Conversationalist" },
];

const mockTwitterAccounts = [
  { id: "twitter-1", name: "@YourMainAccount" },
  { id: "twitter-2", name: "@YourBrandAccount" },
];

export default function EditorPage() {
  const searchParams = useSearchParams();

  const [tweetContent, setTweetContent] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSchedule, setShowSchedule] = useState(false);
  const [mediaFiles, setMediaFiles] = useState<UploadedFile[]>([]);
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content: "",
      twitterAccountId: mockTwitterAccounts[0].id,
      botId: "",
      aiPrompt: "",
      scheduledFor: undefined,
      media: [],
    },
  });

  useEffect(() => {
    if (searchParams) {
      const botId = searchParams.get("bot");
      if (botId) {
        form.setValue("botId", botId);
      }
    }
  }, [searchParams, form]);

  const characterCount = form.watch("content")?.length || 0;
  const selectedBot = form.watch("botId");

  const handleGenerate = async () => {
    setIsGenerating(true);

    // Mock generation delay
    setTimeout(() => {
      const aiPrompt = form.getValues("aiPrompt");
      const botId = form.getValues("botId");

      // Mock AI-generated content
      let generatedContent = "";

      if (botId === "1") {
        generatedContent = "Excited to announce our latest feature! Boost your productivity with our new AI-powered assistant. Try it now and experience the difference. #ProductLaunch #Innovation";
      } else if (botId === "2") {
        generatedContent = "Hey folks! Just dropped an awesome new feature - our AI assistant is here to make your life easier! Check it out and let me know what you think 😊 #NewFeature";
      } else if (botId === "3") {
        generatedContent = "NEW TECH ALERT: Our AI assistant leverages transformer architecture with 175B parameters to deliver contextually relevant responses in real-time. The future is now! #TechNews #AI";
      } else if (botId === "4") {
        generatedContent = "Your journey to success begins with a single step. Our new AI assistant is here to guide you on that journey. Embrace the possibilities and unlock your full potential! #Motivation #Growth";
      } else {
        generatedContent = "Check out our latest feature! Our new AI assistant is now available to help you with your tasks. #NewFeature #AI";
      }

      form.setValue("content", generatedContent);
      setTweetContent(generatedContent);
      setIsGenerating(false);
    }, 1500);
  };

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    try {
      const { content, twitterAccountId, botId, scheduledFor, media } = data;

      // Extract media IDs from uploaded files
      const mediaIds = media?.map((file) => file.id) || [];

      let response;

      if (scheduledFor) {
        // Schedule the tweet
        response = await fetch("/api/tweets/schedule", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content,
            scheduledAt: scheduledFor.toISOString(),
            twitterAccountId,
            mediaIds,
            botPersonaId: botId || undefined,
          }),
        });
      } else {
        // Publish immediately
        response = await fetch("/api/tweets/publish", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content,
            twitterAccountId,
            mediaIds,
          }),
        });
      }

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || "Failed to publish tweet");
      }

      // Show success message
      if (scheduledFor) {
        toast.success(`Your tweet has been scheduled for ${scheduledFor.toLocaleString()}`);
      } else {
        toast.success("Your tweet has been published successfully!");
      }

      // Reset form
      form.reset();
      setTweetContent("");
      setShowSchedule(false);
      setMediaFiles([]);

      // Redirect to schedule list if it was scheduled
      if (scheduledFor) {
        router.push("/dashboard/schedule/list");
      }
    } catch (error) {
      console.error("Error publishing/scheduling tweet:", error);
      toast.error(error instanceof Error ? error.message : "Failed to process your request");
    }
  };

  const handleMediaUpload = (files: UploadedFile[]) => {
    setMediaFiles(files);
    form.setValue("media", files);
  };

  const handleEmojiSelect = (emoji: string) => {
    const currentContent = form.getValues("content") || "";
    form.setValue("content", `${currentContent}${emoji}`);
    setTweetContent(`${currentContent}${emoji}`);
  };

  return (
    <div className="flex flex-col gap-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Compose Tweet</h1>
        <p className="text-muted-foreground">
          Create new content with AI assistance or from scratch
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tweet Editor</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="content"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="relative">
                            <Textarea
                              placeholder="What's happening?"
                              className="min-h-[120px] resize-none pr-10"
                              {...field}
                              onChange={(e) => {
                                field.onChange(e);
                                setTweetContent(e.target.value);
                              }}
                            />
                            <div className="absolute bottom-2 right-2">
                              <EmojiPicker onEmojiSelect={handleEmojiSelect} />
                            </div>
                          </div>
                        </FormControl>
                        <div className="flex justify-between items-center">
                          <FormMessage />
                          <p className={`text-xs ${characterCount > 280 ? "text-destructive" : "text-muted-foreground"}`}>
                            {characterCount}/280
                          </p>
                        </div>
                      </FormItem>
                    )}
                  />

                  <MediaUploader
                    endpoint="tweetMediaUploader"
                    onFilesUploaded={handleMediaUpload}
                    existingFiles={mediaFiles}
                  />

                  <FormField
                    control={form.control}
                    name="twitterAccountId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Twitter Account</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a Twitter account" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {mockTwitterAccounts.map((account) => (
                              <SelectItem key={account.id} value={account.id}>
                                {account.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />

                  <div className="flex items-center justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setShowSchedule(!showSchedule)}
                    >
                      <Clock className="mr-2 h-4 w-4" />
                      Schedule
                    </Button>
                    <Button
                      type="submit"
                      disabled={characterCount === 0 && mediaFiles.length === 0 || characterCount > 280}
                    >
                      {showSchedule ? (
                        <>
                          <Calendar className="mr-2 h-4 w-4" />
                          Schedule Tweet
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" />
                          Publish Now
                        </>
                      )}
                    </Button>
                  </div>

                  {showSchedule && (
                    <FormField
                      control={form.control}
                      name="scheduledFor"
                      render={({ field }) => (
                        <FormItem>
                          <ScheduleSelector value={field.value} onChange={field.onChange} />
                        </FormItem>
                      )}
                    />
                  )}
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <TweetPreview
                content={tweetContent}
                media={mediaFiles.map(file => file.fileURL)}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}