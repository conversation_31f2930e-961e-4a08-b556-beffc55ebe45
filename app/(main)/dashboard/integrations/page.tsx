"use client";

import { TwitterConnect } from "@/components/dashboard/integrations/twitter-connect";
import { GoogleConnect } from "@/components/dashboard/integrations/google-connect";
import { PageTitle } from "@/components/dashboard/page-title";
import { AiProviderSettings } from "@/components/dashboard/integrations/ai-provider-settings";

export default function IntegrationsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <PageTitle
        title="Integrations"
        subtitle="Connect your accounts and configure AI providers"
      />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
        <TwitterConnect />
        <GoogleConnect />
      </div>
      <div className="mt-8">
        <AiProviderSettings />
      </div>
    </div>
  );
}
