import { Metada<PERSON> } from "next";
import { BrainEntryForm } from "@/components/brain/brain-entry-form";

export const metadata: Metadata = {
  title: "Edit Entry - Tweet Brain",
  description: "Edit knowledge base entry",
};

interface EditEntryPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EditEntryPage({ params }: EditEntryPageProps) {
  const resolvedParams = await params;

  return (
    <div className="flex flex-col gap-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Edit Entry</h2>
        <p className="text-muted-foreground">
          Update your knowledge base entry
        </p>
      </div>

      <BrainEntryForm entryId={resolvedParams.id} />
    </div>
  );
}
