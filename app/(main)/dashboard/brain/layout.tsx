"use client";

import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import { Brain, BookOpen, FolderOpen, Plus, Search } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

const navigation = [
  {
    name: "View All",
    value: "view",
    href: "/dashboard/brain/view",
    icon: BookOpen,
    description: "Browse all entries"
  },
  {
    name: "Categories",
    value: "categories",
    href: "/dashboard/brain/categories",
    icon: FolderOpen,
    description: "Manage categories"
  },
  {
    name: "Search",
    value: "search",
    href: "/dashboard/brain/search",
    icon: Search,
    description: "Semantic search"
  },
];

interface BrainLayoutProps {
  children: React.ReactNode;
}

export default function BrainLayout({ children }: BrainLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();

  // Determine current tab from pathname
  const getCurrentTab = () => {
    if (!pathname) return "view"; // Default if pathname is null
    if (pathname.includes("/categories")) return "categories";
    if (pathname.includes("/search")) return "search";
    return "view"; // default to view
  };

  const handleTabChange = (value: string) => {
    const navItem = navigation.find(item => item.value === value);
    if (navItem) {
      router.push(navItem.href);
    }
  };

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <Brain className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Tweet Brain</h1>
            <p className="text-muted-foreground">
              Your AI-powered knowledge base for tweet content
            </p>
          </div>
        </div>
        <Link href="/dashboard/brain/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Entry
          </Button>
        </Link>
      </div>

      {/* Navigation Tabs */}
      <Tabs value={getCurrentTab()} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          {navigation.map((item) => (
            <TabsTrigger
              key={item.value}
              value={item.value}
              className="flex items-center gap-2"
            >
              <item.icon className="h-4 w-4" />
              {item.name}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      {/* Content */}
      <div className="flex-1">
        {children}
      </div>
    </div>
  );
}
