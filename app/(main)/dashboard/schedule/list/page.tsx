"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { Plus, Edit, Trash2, Calendar, ListFilter, ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { format } from "date-fns";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { TweetPreview } from "@/components/tweet/tweet-preview";
import { truncateText } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { toast } from "sonner";

// Define types for API responses
interface TwitterAccount {
  id: string;
  name: string;
  screenName: string;
  profilePicture: string | null;
}

interface BotPersona {
  id: string;
  name: string;
  description: string | null;
}

interface ScheduledTweet {
  id: string;
  content: string;
  scheduledAt: string;
  status: 'PENDING' | 'PUBLISHED' | 'FAILED';
  userId: string;
  twitterAccountId: string;
  botPersonaId: string | null;
  mediaUrls: string[];
  twitterAccount: TwitterAccount;
  botPersona: BotPersona | null;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasMore: boolean;
}

interface ApiResponse {
  success: boolean;
  tweets: ScheduledTweet[];
  pagination: PaginationData;
}

export default function SchedulePage() {
  const [scheduledTweets, setScheduledTweets] = useState<ScheduledTweet[]>([]);
  const [selectedTweet, setSelectedTweet] = useState<string | null>(null);
  const [tweetToDelete, setTweetToDelete] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [pagination, setPagination] = useState<PaginationData | null>(null);
  const tweetsPerPage = 5;

  const fetchTweets = useCallback(async () => {
    setIsLoading(true);
    try {
      const statusFilter = filterStatus !== 'all' ? `&status=${filterStatus.toUpperCase()}` : '';
      const response = await fetch(`/api/tweets/scheduled?page=${currentPage}&limit=10${statusFilter}`);

      if (!response.ok) {
        throw new Error('Failed to fetch scheduled tweets');
      }

      const data: ApiResponse = await response.json();

      setScheduledTweets(data.tweets);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching scheduled tweets:', error);
      toast.error('Failed to load scheduled tweets');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, filterStatus]);

  useEffect(() => {
    fetchTweets();
  }, [fetchTweets]);

  const handleDeleteTweet = async () => {
    if (tweetToDelete) {
      try {
        setIsDeleting(true);

        const response = await fetch(`/api/tweets/scheduled/${tweetToDelete}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('Failed to delete tweet');
        }

        const data = await response.json();

        if (data.success) {
          toast.success("The scheduled tweet has been deleted successfully.");

          // Refresh tweets
          fetchTweets();

          if (selectedTweet === tweetToDelete) {
            setSelectedTweet(null);
          }
        } else {
          throw new Error(data.error || 'Failed to delete tweet');
        }
      } catch (error) {
        console.error('Error deleting tweet:', error);
        toast.error(error instanceof Error ? error.message : "Failed to delete the tweet.");
      } finally {
        setTweetToDelete(null);
        setIsDeleting(false);
      }
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const selectedTweetData = scheduledTweets.find(tweet => tweet.id === selectedTweet);

  return (
    <>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Scheduled Tweets</h1>
            <p className="text-muted-foreground">
              Manage your upcoming scheduled tweets
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-[180px]">
                <ListFilter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter by Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <Link href="/dashboard/publish/editor">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Tweet
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Tweets</CardTitle>
                <CardDescription>
                  Your scheduled posts for the coming days
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[150px] md:w-[180px]">Scheduled For</TableHead>
                        <TableHead className="hidden md:table-cell w-[30%]">Content</TableHead>
                        <TableHead className="hidden lg:table-cell">Bot</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {scheduledTweets.map((tweet) => (
                        <TableRow
                          key={tweet.id}
                          onClick={() => setSelectedTweet(tweet.id)}
                          className={selectedTweet === tweet.id ? "bg-muted/50 cursor-pointer" : "cursor-pointer hover:bg-muted/30"}
                        >
                          <TableCell className="font-medium">
                            <div className="flex flex-col">
                              <span>{format(new Date(tweet.scheduledAt), 'MMM d, yyyy')}</span>
                              <span className="text-xs text-muted-foreground">
                                {format(new Date(tweet.scheduledAt), 'h:mm a')}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">
                            <div className="flex items-center">
                              <span className="line-clamp-1">{truncateText(tweet.content, 60)}</span>
                              {tweet.mediaUrls?.length > 0 && (
                                <Badge variant="outline" className="ml-2">
                                  Media
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell">
                            {tweet.botPersona ? (
                              <Badge variant="secondary">
                                {tweet.botPersona.name}
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground text-sm">None</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                tweet.status === "PUBLISHED" ? "default" :
                                  tweet.status === "FAILED" ? "destructive" :
                                    "outline"
                              }
                            >
                              {tweet.status.charAt(0).toUpperCase() + tweet.status.slice(1).toLowerCase()}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                asChild
                                onClick={(e) => e.stopPropagation()}
                                disabled={tweet.status !== "PENDING"}
                              >
                                <Link href={`/dashboard/publish/editor?edit=${tweet.id}`}>
                                  <Edit className="h-4 w-4" />
                                  <span className="sr-only">Edit</span>
                                </Link>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setTweetToDelete(tweet.id);
                                }}
                                disabled={tweet.status !== "PENDING"}
                              >
                                <Trash2 className="h-4 w-4" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                      {!isLoading && scheduledTweets.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={5} className="h-24 text-center">
                            <div className="flex flex-col items-center justify-center gap-2">
                              <Calendar className="h-8 w-8 text-muted-foreground" />
                              <div>No scheduled tweets</div>
                              <Link href="/dashboard/publish/editor">
                                <Button variant="outline" size="sm">
                                  Create one
                                </Button>
                              </Link>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
            {pagination && pagination.totalPages > 0 && (
              <div className="flex justify-center mt-4">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage > 1) handlePageChange(currentPage - 1);
                        }}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : undefined}
                      />
                    </PaginationItem>
                    {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(page);
                          }}
                          isActive={page === currentPage}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                    <PaginationItem>
                      <PaginationNext
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage < pagination.totalPages) handlePageChange(currentPage + 1);
                        }}
                        className={currentPage === pagination.totalPages ? "pointer-events-none opacity-50" : undefined}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Tweet Preview</CardTitle>
              <CardDescription>
                {selectedTweetData
                  ? `Scheduled for ${format(new Date(selectedTweetData.scheduledAt), 'MMM d, yyyy')} at ${format(new Date(selectedTweetData.scheduledAt), 'h:mm a')}`
                  : "Select a tweet to preview"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedTweetData ? (
                <TweetPreview
                  content={selectedTweetData.content}
                  media={selectedTweetData.mediaUrls || []}
                />
              ) : (
                <div className="flex h-[200px] items-center justify-center rounded-md border border-dashed">
                  <div className="flex flex-col items-center gap-1 text-center">
                    <Calendar className="h-8 w-8 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">
                      Select a scheduled tweet to preview
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
            {selectedTweetData && selectedTweetData.status === "PENDING" && (
              <CardFooter className="border-t bg-muted/50 flex justify-between">
                <Link href={`/dashboard/publish/editor?edit=${selectedTweetData.id}`}>
                  <Button variant="outline">
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  className="text-destructive hover:text-destructive hover:bg-destructive/10"
                  onClick={() => setTweetToDelete(selectedTweetData.id)}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </CardFooter>
            )}
          </Card>
        </div>
      </div>

      <AlertDialog open={!!tweetToDelete} onOpenChange={() => setTweetToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this scheduled tweet.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteTweet}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}