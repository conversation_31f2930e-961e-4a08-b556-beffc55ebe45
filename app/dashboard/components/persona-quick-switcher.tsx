"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { BotPersona } from "@prisma/client"
import { PersonaSwitcher } from "@/components/bots/persona-switcher"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { motion } from "framer-motion"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export function PersonaQuickSwitcher() {
  // State
  const [error, setError] = useState<string | null>(null)
  
  // Handle persona change
  const handlePersonaChange = async (persona: BotPersona) => {
    try {
      // You can add additional logic here if needed
      // For example, updating other components or triggering events
      
      // For now, we'll just log the change
      console.log("Persona changed to:", persona.name)
    } catch (error) {
      console.error("Error handling persona change:", error)
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
    }
  }
  
  return (
    <motion.div 
      className="space-y-2"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div>
              <PersonaSwitcher 
                onPersonaChange={handlePersonaChange}
                className="w-full"
              />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Switch between different bot personas</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      
      {error && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
        >
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </motion.div>
      )}
    </motion.div>
  )
}
