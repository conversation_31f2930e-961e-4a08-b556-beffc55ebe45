"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { BotPersona } from "@prisma/client"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Bot, Settings, Plus, X, Search, CheckCircle } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { PersonaQuickSwitcher } from "./persona-quick-switcher"

export function PersonaSelectorPanel() {
  // State
  const [activePersona, setActivePersona] = useState<BotPersona | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [activePersonaId, setActivePersonaId] = useState<string | null>(null)

  // Fetch active persona on mount
  useEffect(() => {
    fetchActivePersona()
  }, [])

  // Fetch active persona from API
  const fetchActivePersona = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("/api/bots/personas?active=true")

      if (!response.ok) {
        throw new Error("Failed to fetch active persona")
      }

      const data = await response.json()

      if (data.personas && data.personas.length > 0) {
        setActivePersona(data.personas[0])
        setActivePersonaId(data.personas[0].id)
      }
    } catch (error) {
      console.error("Error fetching active persona:", error)
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
    } finally {
      setLoading(false)
    }
  }

  // Get avatar fallback text (initials)
  const getAvatarFallback = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
  }

  // Get bio text for display
  const getBioText = (persona: BotPersona) => {
    try {
      const content = persona.personaContent as any;
      if (content?.bio && Array.isArray(content.bio) && content.bio.length > 0) {
        return content.bio[0];
      }
    } catch (error) {
      console.error('Error parsing persona content:', error);
    }

    return persona.description || "No description available";
  }

  // Get topics for display
  const getTopics = (persona: BotPersona): string[] => {
    try {
      const content = persona.personaContent as any;
      return content?.topics
        ? Array.isArray(content.topics)
          ? content.topics.slice(0, 3)
          : []
        : [];
    } catch (error) {
      console.error('Error parsing persona content:', error);
      return [];
    }
  }

  // Get system prompt for display
  const getSystemPrompt = (persona: BotPersona) => {
    try {
      const content = persona.personaContent as any;
      return content?.system || "";
    } catch (error) {
      console.error('Error parsing persona content:', error);
      return "";
    }
  }

  // Filter personas based on search query
  const filteredPersonas = activePersona ? [activePersona] : []

  // Handle persona selection
  const handleSelectPersona = (persona: BotPersona) => {
    setActivePersona(persona)
    setActivePersonaId(persona.id)
  }

  // Handle create persona
  const handleCreatePersona = () => {
    // Implement create persona logic here
  }

  // Handle close button click
  const onClose = () => {
    // Implement close button logic here
  }

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name.split(" ").map((word) => word.charAt(0).toUpperCase()).join("")
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Active Persona</CardTitle>
        <CardDescription>
          The currently active persona for content generation
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <PersonaQuickSwitcher />

        {loading ? (
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-1">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-32" />
              </div>
            </div>
            <Skeleton className="h-16 w-full" />
          </div>
        ) : activePersona ? (
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarFallback>
                  {getAvatarFallback(activePersona.name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-medium">{activePersona.name}</h3>
                <p className="text-sm text-muted-foreground truncate max-w-[200px]">
                  {getBioText(activePersona)}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex flex-wrap gap-1">
                {getTopics(activePersona).map((topic, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {topic}
                  </Badge>
                ))}
              </div>

              <p className="text-sm text-muted-foreground line-clamp-2">
                {(() => {
                  const systemPrompt = getSystemPrompt(activePersona);
                  return systemPrompt
                    ? systemPrompt.substring(0, 120) + (systemPrompt.length > 120 ? "..." : "")
                    : "No system prompt defined";
                })()}
              </p>
            </div>
          </div>
        ) : (
          <div className="text-center py-4 space-y-3">
            <Bot className="h-10 w-10 mx-auto text-muted-foreground" />
            <p className="text-sm text-muted-foreground">No active persona selected</p>
          </div>
        )}

        <div className="flex flex-col space-y-2 pt-2">
          <Link href="/bots/personas" passHref>
            <Button variant="outline" size="sm" className="w-full">
              <Settings className="h-4 w-4 mr-2" />
              Manage Personas
            </Button>
          </Link>

          <Link href="/bots/personas?create=true" passHref>
            <Button size="sm" className="w-full">
              <Plus className="h-4 w-4 mr-2" />
              Create New Persona
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
