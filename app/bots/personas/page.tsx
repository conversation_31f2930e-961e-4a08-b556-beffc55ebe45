"use client"

import { useState, useEffect, use<PERSON>allback, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { BotPersona } from "@prisma/client"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

// Enhanced BotPersona interface - use the base type directly since it already has the enhanced fields
type EnhancedBotPersona = BotPersona;

import { PersonaGrid } from "@/components/bots/persona-grid"
import { PersonaEditor } from "@/components/bots/persona-editor"
import { PersonaImportExport } from "@/components/bots/persona-import-export"
import { PersonaFileUpload } from "@/components/bots/persona-file-upload"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, ArrowLeft } from "lucide-react"
import { EnhancedPersonaTemplate, PersonaI<PERSON>rtResult, PersonaExportResult } from "@/lib/bots/types"
import { toast } from "sonner"

function PersonasPageContent() {
  // Router and search params
  const router = useRouter()
  const searchParams = useSearchParams()
  const createParam = searchParams?.get("create")
  const importParam = searchParams?.get("import")

  // State
  const [personas, setPersonas] = useState<EnhancedBotPersona[]>([]);
  const [activePersonaId, setActivePersonaId] = useState<string | undefined>();
  const [isLoading, setIsLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState('updatedAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [isCreating, setIsCreating] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [editingPersona, setEditingPersona] = useState<EnhancedBotPersona | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const [loading, setLoading] = useState(true);
  const itemsPerPage = 12;

  // Initialize state based on URL params
  useEffect(() => {
    if (createParam === "true") {
      setIsCreating(true)
    }
    if (importParam === "true") {
      setIsImporting(true)
    }
  }, [createParam, importParam])

  // Fetch personas
  const fetchPersonas = useCallback(async (
    query?: string,
    sort?: string,
    order?: string,
    page?: number
  ) => {
    setIsLoading(true);
    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (query) params.append('search', query);
      if (sort) params.append('sort', sort);
      if (order) params.append('order', order);
      if (page) params.append('page', page.toString());
      params.append('limit', itemsPerPage.toString());

      const url = `/api/bots/personas${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Failed to fetch personas');
      }

      const data = await response.json();
      setPersonas(data.personas || data);
      setTotalCount(data.pagination?.totalCount || data.length || 0);
    } catch (error) {
      console.error('Error fetching personas:', error);
      toast.error("Failed to load personas. Please try again.");
    } finally {
      setIsLoading(false);
      setLoading(false);
    }
  }, [itemsPerPage]);

  // Handle delete
  const handleDelete = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/bots/personas/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete persona');
      }

      toast.success("Persona deleted successfully");

      fetchPersonas();
    } catch (error) {
      console.error('Error deleting persona:', error);
      toast.error("Failed to delete persona. Please try again.");
    }
  }, [fetchPersonas]);

  // Load personas on mount
  useEffect(() => {
    fetchPersonas();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle search, sort, and pagination
  const handleSearch = useCallback(async (query: string, sort: string, page: number) => {
    setSearchQuery(query);
    setCurrentPage(page);
    await fetchPersonas(query, sort, sortOrder, page);
  }, [fetchPersonas, sortOrder]);

  const handleSort = useCallback(async (query: string, sort: string, page: number) => {
    setSortField(sort);
    setCurrentPage(page);
    await fetchPersonas(query, sort, sortOrder, page);
  }, [fetchPersonas, sortOrder]);

  const handlePageChange = useCallback(async (query: string, sort: string, page: number) => {
    setCurrentPage(page);
    await fetchPersonas(query, sort, sortOrder, page);
  }, [fetchPersonas, sortOrder]);

  // Fetch personas on mount
  useEffect(() => {
    fetchPersonas()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Handle persona selection (set as active)
  const handleSelectPersona = async (persona: EnhancedBotPersona) => {
    try {
      setError(null)

      const response = await fetch(`/api/bots/personas/${persona.id}/active`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }
      })

      if (!response.ok) {
        throw new Error("Failed to set active persona")
      }

      setActivePersonaId(persona.id)

      // Refresh personas to update UI
      await fetchPersonas()
    } catch (error) {
      console.error("Error setting active persona:", error)
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
    }
  }

  // Handle persona edit
  const handleEditPersona = (persona: EnhancedBotPersona) => {
    setEditingPersona(persona)
    setIsCreating(false)
  }

  // Handle persona delete
  const handleDeletePersona = async (persona: EnhancedBotPersona) => {
    if (!confirm(`Are you sure you want to delete "${persona.name}"?`)) {
      return
    }

    try {
      setError(null)

      const response = await fetch(`/api/bots/personas/${persona.id}`, {
        method: "DELETE"
      })

      if (!response.ok) {
        throw new Error("Failed to delete persona")
      }

      // Refresh personas to update UI
      await fetchPersonas()
    } catch (error) {
      console.error("Error deleting persona:", error)
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
    }
  }

  // Handle persona export
  const handleExportPersona = async (persona: EnhancedBotPersona): Promise<PersonaExportResult> => {
    try {
      const response = await fetch(`/api/bots/personas/${persona.id}/export`)

      if (!response.ok) {
        throw new Error("Failed to export persona")
      }

      const data = await response.json()

      return {
        success: true,
        data: data.data
      }
    } catch (error) {
      console.error("Error exporting persona:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "An unexpected error occurred"
      }
    }
  }

  // Handle persona import
  const handleImportPersona = async (jsonData: string): Promise<PersonaImportResult> => {
    try {
      const response = await fetch("/api/bots/personas/import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ jsonData })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to import persona")
      }

      const data = await response.json()

      // Refresh personas to update UI
      await fetchPersonas()

      return {
        success: true,
        personaId: data.persona?.id
      }
    } catch (error) {
      console.error("Error importing persona:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "An unexpected error occurred"
      }
    }
  }

  // Handle persona save (create or update)
  const handleSavePersona = async (personaData: EnhancedPersonaTemplate): Promise<EnhancedBotPersona | null> => {
    try {
      setError(null)

      let response;

      if (editingPersona) {
        // Update existing persona
        response = await fetch(`/api/bots/personas/${editingPersona.id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(personaData)
        })
      } else {
        // Create new persona
        response = await fetch("/api/bots/personas", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(personaData)
        })
      }

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to save persona")
      }

      const data = await response.json()

      // Reset editing state
      setEditingPersona(null)
      setIsCreating(false)

      // Update URL to remove create parameter
      if (createParam === "true") {
        router.replace("/bots/personas")
      }

      // Refresh personas to update UI
      await fetchPersonas()

      return data.persona
    } catch (error) {
      console.error("Error saving persona:", error)
      setError(error instanceof Error ? error.message : "An unexpected error occurred")
      return null
    }
  }

  // Handle cancel edit/create/import
  const handleCancelEdit = () => {
    setEditingPersona(null)
    setIsCreating(false)
    setIsImporting(false)

    // Update URL to remove parameters
    if (createParam === "true" || importParam === "true") {
      router.replace("/bots/personas")
    }
  }

  // Handle import success
  const handleImportSuccess = async (result: PersonaImportResult) => {
    // Refresh personas to update UI
    await fetchPersonas()

    // Reset importing state
    setTimeout(() => {
      setIsImporting(false)
      router.replace("/bots/personas")
    }, 2000)
  }

  // Handle create new persona
  const handleCreateNew = () => {
    setEditingPersona(null)
    setIsCreating(true)
  }

  // Get active persona
  const getActivePersona = () => {
    return personas.find(p => p.id === activePersonaId) || null
  }

  // If editing or creating, show the editor
  if (editingPersona || isCreating) {
    return (
      <div className="container py-6">
        <PersonaEditor
          persona={editingPersona}
          onSave={handleSavePersona}
          onCancel={handleCancelEdit}
        />
      </div>
    )
  }

  // If importing, show the file upload
  if (isImporting) {
    return (
      <div className="container py-6">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center">
            <Button variant="ghost" onClick={handleCancelEdit}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Personas
            </Button>
            <h1 className="text-3xl font-bold ml-4">Import Persona</h1>
          </div>

          <div className="max-w-md mx-auto w-full py-8">
            <PersonaFileUpload
              onImport={handleImportPersona}
              onSuccess={handleImportSuccess}
              className="w-full"
            />
          </div>
        </div>
      </div>
    )
  }

  // Main personas page
  return (
    <div className="container py-6">
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button variant="ghost" onClick={() => router.push("/dashboard/home")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
            <h1 className="text-3xl font-bold ml-4">Bot Personas</h1>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setIsImporting(true)
                router.push("/bots/personas?import=true")
              }}
            >
              Import Persona
            </Button>
            <PersonaImportExport
              onImport={handleImportPersona}
              onExport={handleExportPersona}
              activePersona={getActivePersona()}
            />
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="all">All Personas</TabsTrigger>
            <TabsTrigger value="active">Active Persona</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-4">
            {loading ? (
              <div className="text-center py-12">Loading personas...</div>
            ) : (
              <PersonaGrid
                personas={personas}
                activePersonaId={activePersonaId || undefined}
                onSelect={handleSelectPersona}
                onEdit={handleEditPersona}
                onDelete={handleDeletePersona}
                onExport={handleExportPersona}
                onCreateNew={handleCreateNew}
              />
            )}
          </TabsContent>

          <TabsContent value="active" className="mt-4">
            {loading ? (
              <div className="text-center py-12">Loading active persona...</div>
            ) : activePersonaId ? (
              <PersonaGrid
                personas={personas.filter(p => p.id === activePersonaId)}
                activePersonaId={activePersonaId}
                onSelect={handleSelectPersona}
                onEdit={handleEditPersona}
                onDelete={handleDeletePersona}
                onExport={handleExportPersona}
                onCreateNew={handleCreateNew}
              />
            ) : (
              <div className="text-center py-12 border rounded-lg bg-muted/10">
                <p className="text-muted-foreground mb-4">No active persona set</p>
                <Button onClick={handleCreateNew}>Create New Persona</Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default function PersonasPage() {
  return (
    <Suspense fallback={<div className="container py-6"><div className="text-center">Loading...</div></div>}>
      <PersonasPageContent />
    </Suspense>
  )
}
