import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from '@/components/providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'xtasker - AI-Powered Twitter/X Management',
  description: 'Comprehensive AI-assisted Twitter/X management platform for composing, scheduling, and publishing tweets with intelligent bot personas and semantic knowledge base.',
  keywords: ['Twitter', 'X', 'AI', 'Social Media', 'Management', 'Automation', 'Bot', 'Scheduling'],
  authors: [{ name: 'xtasker Team' }],
  creator: 'xtasker',
  publisher: 'xtasker',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
