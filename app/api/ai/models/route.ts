import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { AIProviderFactory } from '@/lib/ai/providers';

const modelsRequestSchema = z.object({
  provider: z.enum(['OPENAI', 'GOOGLE']),
  apiKey: z.string().min(1, 'API key is required'),
  type: z.enum(['chat', 'embedding']).optional(),
});

/**
 * POST /api/ai/models - Get available models from AI providers
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { provider, apiKey, type } = modelsRequestSchema.parse(body);

    try {
      // Get available models from the provider
      const models = await AIProviderFactory.getAvailableModels(provider, apiKey);

      // Filter by type if specified
      let filteredModels = models;
      if (type === 'chat') {
        filteredModels = models.filter(model =>
          !model.includes('embedding') &&
          !model.includes('whisper') &&
          !model.includes('tts') &&
          !model.includes('dall-e')
        );
      } else if (type === 'embedding') {
        filteredModels = models.filter(model =>
          model.includes('embedding')
        );
      }

      if (filteredModels.length === 0) {
        return NextResponse.json(
          {
            error: `No ${type || ''} models found for provider ${provider}`,
            details: 'The API returned an empty list of models for your key and filter criteria.'
          },
          { status: 404 }
        );
      }

      return NextResponse.json(filteredModels);
    } catch (providerError) {
      console.error(`Error fetching models from ${provider}:`, providerError);
      return NextResponse.json(
        {
          error: `Failed to fetch models from ${provider}`,
          details: providerError instanceof Error ? providerError.message : 'Unknown provider error'
        },
        { status: 502 }
      );
    }
  } catch (error) {
    console.error('Models API error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to fetch models',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
