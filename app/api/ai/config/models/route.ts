import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { validateAuth } from "@/lib/middleware/auth";
import { OpenAIProvider } from "@/lib/ai/providers/openai";
import { GoogleGenAIProvider } from "@/lib/ai/providers/google-genai";

// Simple decryption for API keys (matches encryption in keys route)
function decryptKey(encryptedKey: Buffer): string {
  return encryptedKey.toString('utf-8');
}

export async function GET(request: NextRequest) {
  try {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get user's provider configurations
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        openAiApiKey: true,
        googleApiKey: true,
        defaultChatModel: true,
        defaultEmbeddingModel: true,
        defaultAiProvider: true
      }
    });

    if (!userData) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    const availableModels: {
      provider: string;
      chat: string[];
      embedding: string[];
      isConfigured: boolean;
      error?: string;
    }[] = [];

    // Fetch OpenAI models if configured
    if (userData.openAiApiKey) {
      try {
        const decryptedKey = decryptKey(userData.openAiApiKey);
        const openaiProvider = new OpenAIProvider({
          apiKey: decryptedKey,
          defaultChatModel: "gpt-4-turbo-preview",
          defaultEmbeddingModel: "text-embedding-3-small"
        });

        const models = await openaiProvider.getAvailableModels();
        availableModels.push({
          provider: "OPENAI",
          chat: models.chat,
          embedding: models.embedding,
          isConfigured: true
        });
      } catch (error) {
        console.error("Failed to fetch OpenAI models:", error);
        availableModels.push({
          provider: "OPENAI",
          chat: [],
          embedding: [],
          isConfigured: true,
          error: error instanceof Error ? error.message : "Failed to fetch models"
        });
      }
    } else {
      availableModels.push({
        provider: "OPENAI",
        chat: [],
        embedding: [],
        isConfigured: false
      });
    }

    // Fetch Google AI models if configured
    if (userData.googleApiKey) {
      try {
        const decryptedKey = decryptKey(userData.googleApiKey);
        const googleProvider = new GoogleGenAIProvider({
          apiKey: decryptedKey,
          defaultChatModel: userData.defaultChatModel || "gemini-1.5-pro",
          defaultEmbeddingModel: userData.defaultEmbeddingModel || "text-embedding-004",
        });

        const models = await googleProvider.getAvailableModels();
        availableModels.push({
          provider: "GOOGLE",
          chat: models.chat,
          embedding: models.embedding,
          isConfigured: true
        });
      } catch (error) {
        console.error("Failed to fetch Google AI models:", error);
        availableModels.push({
          provider: "GOOGLE",
          chat: [],
          embedding: [],
          isConfigured: true,
          error: error instanceof Error ? error.message : "Failed to fetch models"
        });
      }
    } else {
      availableModels.push({
        provider: "GOOGLE",
        chat: [],
        embedding: [],
        isConfigured: false
      });
    }

    return NextResponse.json({
      providers: availableModels,
      currentSettings: {
        provider: userData.defaultAiProvider,
        chatModel: userData.defaultChatModel,
        embeddingModel: userData.defaultEmbeddingModel
      }
    });
  } catch (error) {
    console.error("Failed to fetch AI models:", error);
    return NextResponse.json(
      { error: "Failed to fetch AI models" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { provider, chatModel, embeddingModel } = body;

    if (!provider || !chatModel || !embeddingModel) {
      return NextResponse.json(
        { error: "Provider, chat model, and embedding model are required" },
        { status: 400 }
      );
    }

    // Update user's model preferences
    await prisma.user.update({
      where: { id: user.id },
      data: {
        defaultAiProvider: provider,
        defaultChatModel: chatModel,
        defaultEmbeddingModel: embeddingModel,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: "Model preferences updated successfully"
    });
  } catch (error) {
    console.error("Failed to update model preferences:", error);
    return NextResponse.json(
      { error: "Failed to update model preferences" },
      { status: 500 }
    );
  }
}
