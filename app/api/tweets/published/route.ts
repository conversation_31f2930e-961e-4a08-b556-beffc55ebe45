import { NextRequest } from 'next/server';
import { authenticateUser, createAuthenticatedResponse, createErrorResponse } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { TweetStatus } from '@prisma/client';
import { z } from 'zod';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const listQuerySchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).default('1'),
  limit: z.string().regex(/^\d+$/).transform(Number).default('10'),
  twitterAccountId: z.string().optional(),
  startDate: z.string().optional().transform(str => str ? new Date(str) : undefined),
  endDate: z.string().optional().transform(str => str ? new Date(str) : undefined)
}).optional();

export async function GET(req: NextRequest) {
  try {
    // Skip execution during build time
    if (process.env.NODE_ENV === 'production' && !req.headers.get('user-agent')) {
      return createErrorResponse('Not available during build', 503);
    }

    const user = await authenticateUser(req);

    // Parse query parameters
    const searchParams = Object.fromEntries(req.nextUrl.searchParams);
    const parsed = listQuerySchema.safeParse(searchParams);

    if (!parsed.success) {
      return createErrorResponse('Invalid query parameters', 400);
    }

    const {
      page = 1,
      limit = 10,
      twitterAccountId,
      startDate,
      endDate
    } = parsed.data || {};

    const skip = (page - 1) * limit;

    // Build where clause
    const where = {
      userId: user.id,
      status: TweetStatus.PUBLISHED,
      publishedTweetId: { not: null },
      ...(twitterAccountId && { twitterAccountId }),
      ...(startDate && {
        scheduledAt: {
          gte: startDate,
          ...(endDate && { lte: endDate })
        }
      })
    } as const;

    // Get published tweets with pagination
    const [publishedTweets, total] = await Promise.all([
      prisma.scheduledTweet.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          scheduledAt: 'desc' // Most recent first
        },
        include: {
          twitterAccount: {
            select: {
              id: true,
              screenName: true,
              name: true,
              profilePicture: true
            }
          },
          botPersona: {
            select: {
              id: true,
              name: true,
              description: true
            }
          }
        }
      }),
      prisma.scheduledTweet.count({ where })
    ]);

    // Transform tweets and include Twitter URLs
    const transformedTweets = publishedTweets.map(tweet => ({
      ...tweet,
      mediaUrls: tweet.mediaUrlsJson ? JSON.parse(tweet.mediaUrlsJson as string) : [],
      publishedUrl: tweet.publishedTweetId && tweet.twitterAccount
        ? `https://twitter.com/${tweet.twitterAccount.screenName}/status/${tweet.publishedTweetId}`
        : null
    }));

    return createAuthenticatedResponse({
      tweets: transformedTweets,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasMore: skip + limit < total
      }
    });

  } catch (error) {
    console.error('Error listing published tweets:', error);
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    return createErrorResponse('Internal server error', 500);
  }
}