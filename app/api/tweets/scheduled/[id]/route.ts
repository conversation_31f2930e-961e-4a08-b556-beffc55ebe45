import { NextRequest } from 'next/server';
import { authenticateUser, createAuthenticatedResponse, createErrorResponse } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const updateTweetSchema = z.object({
  content: z.string().min(1).max(280).optional(),
  scheduledAt: z.string().transform(str => new Date(str)).optional(),
  mediaIds: z.array(z.string()).optional(),
  botPersonaId: z.string().optional().nullable()
});

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const user = await authenticateUser(req);
    const { id } = resolvedParams;

    // Parse and validate request data
    const json = await req.json();
    const parsed = updateTweetSchema.safeParse(json);

    if (!parsed.success) {
      return createErrorResponse('Invalid request data', 400);
    }

    // Get existing tweet and verify ownership
    const existingTweet = await prisma.scheduledTweet.findFirst({
      where: {
        id,
        userId: user.id
      }
    });

    if (!existingTweet) {
      return createErrorResponse('Tweet not found or access denied', 404);
    }

    // Can only update pending tweets
    if (existingTweet.status !== 'PENDING') {
      return createErrorResponse('Can only update pending tweets', 400);
    }

    const { content, scheduledAt, mediaIds, botPersonaId } = parsed.data;

    // Validate scheduling time if provided
    if (scheduledAt && scheduledAt <= new Date()) {
      return createErrorResponse('Scheduled time must be in the future', 400);
    }

    // Verify bot persona if provided
    if (botPersonaId) {
      const botPersona = await prisma.botPersona.findFirst({
        where: {
          id: botPersonaId,
          userId: user.id
        }
      });

      if (!botPersona) {
        return createErrorResponse('Bot persona not found or access denied', 404);
      }
    }

    // Process media if provided
    let mediaUrlsJson = existingTweet.mediaUrlsJson;
    if (mediaIds) {
      const mediaFiles = await prisma.uploadedFile.findMany({
        where: {
          id: { in: mediaIds },
          userId: user.id
        }
      });

      if (!mediaFiles || mediaFiles.length !== mediaIds.length) {
        return createErrorResponse('One or more media files not found', 404);
      }

      mediaUrlsJson = JSON.stringify(mediaFiles.map(file => file.fileURL));
    }

    // Update tweet
    const updatedTweet = await prisma.scheduledTweet.update({
      where: { id },
      data: {
        ...(content && { content }),
        ...(scheduledAt && { scheduledAt }),
        ...(mediaUrlsJson && { mediaUrlsJson }),
        botPersonaId: botPersonaId === null ? null : botPersonaId || undefined,
      },
      include: {
        twitterAccount: {
          select: {
            id: true,
            screenName: true,
            name: true,
            profilePicture: true
          }
        },
        botPersona: {
          select: {
            id: true,
            name: true,
            description: true
          }
        }
      }
    });

    return createAuthenticatedResponse({
      tweet: {
        ...updatedTweet,
        mediaUrls: updatedTweet.mediaUrlsJson 
          ? JSON.parse(updatedTweet.mediaUrlsJson as string)
          : []
      }
    });

  } catch (error) {
    console.error('Error updating scheduled tweet:', error);
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    return createErrorResponse('Internal server error', 500);
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const user = await authenticateUser(req);
    const { id } = resolvedParams;

    // Verify tweet exists and user owns it
    const tweet = await prisma.scheduledTweet.findFirst({
      where: {
        id,
        userId: user.id
      }
    });

    if (!tweet) {
      return createErrorResponse('Tweet not found or access denied', 404);
    }

    // Can only delete pending tweets
    if (tweet.status !== 'PENDING') {
      return createErrorResponse('Can only delete pending tweets', 400);
    }

    // Delete the tweet
    await prisma.scheduledTweet.delete({
      where: { id }
    });

    return createAuthenticatedResponse({
      message: 'Tweet deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting scheduled tweet:', error);
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    return createErrorResponse('Internal server error', 500);
  }
}