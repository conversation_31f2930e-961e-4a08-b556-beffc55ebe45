import { NextRequest } from 'next/server';
import { authenticateUser, createAuthenticatedResponse, createErrorResponse } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { tweetScheduler } from '@/lib/tweets/scheduler';
import { z } from 'zod';

const scheduleTweetSchema = z.object({
  content: z.string().min(1).max(280),
  scheduledAt: z.string().transform(str => new Date(str)),
  twitterAccountId: z.string(),
  mediaIds: z.array(z.string()).optional(),
  botPersonaId: z.string().optional()
});

export async function POST(req: NextRequest) {
  try {
    // Authenticate user
    const user = await authenticateUser(req);

    // Parse and validate request data
    const json = await req.json();
    const parsed = scheduleTweetSchema.safeParse(json);

    if (!parsed.success) {
      return createErrorResponse('Invalid request data', 400);
    }

    const { content, scheduledAt, twitterAccountId, mediaIds, botPersonaId } = parsed.data;

    // Validate scheduling time (must be in the future)
    if (scheduledAt <= new Date()) {
      return createErrorResponse('Scheduled time must be in the future', 400);
    }

    // Verify Twitter account ownership
    const twitterAccount = await prisma.linkedAccount.findFirst({
      where: {
        id: twitterAccountId,
        userId: user.id,
        provider: 'twitter'
      }
    });

    if (!twitterAccount) {
      return createErrorResponse('Twitter account not found or access denied', 404);
    }

    // Verify bot persona if provided
    if (botPersonaId) {
      const botPersona = await prisma.botPersona.findFirst({
        where: {
          id: botPersonaId,
          userId: user.id
        }
      });

      if (!botPersona) {
        return createErrorResponse('Bot persona not found or access denied', 404);
      }
    }

    // Verify media files if provided
    let mediaUrlsJson;
    if (mediaIds?.length) {
      const mediaFiles = await prisma.uploadedFile.findMany({
        where: {
          id: { in: mediaIds },
          userId: user.id
        }
      });

      if (!mediaFiles || mediaFiles.length !== mediaIds.length) {
        return createErrorResponse('One or more media files not found', 404);
      }

      // Store media URLs for later use
      mediaUrlsJson = JSON.stringify(mediaFiles.map(file => file.fileURL));
    }

    // Create scheduled tweet
    const scheduledTweet = await prisma.scheduledTweet.create({
      data: {
        content,
        scheduledAt,
        twitterAccountId,
        botPersonaId,
        mediaUrlsJson,
        userId: user.id,
        status: 'PENDING'
      }
    });

    // Ensure scheduler is running
    tweetScheduler.start();

    return createAuthenticatedResponse({
      scheduledTweet
    });

  } catch (error) {
    console.error('Error scheduling tweet:', error);
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    return createErrorResponse('Internal server error', 500);
  }
}