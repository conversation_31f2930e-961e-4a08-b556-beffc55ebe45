import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const linkedAccounts = await prisma.linkedAccount.findMany({
      where: {
        userId: session.user.id,
        provider: 'twitter',
      },
      select: {
        id: true,
        providerAccountId: true,
        name: true,
        screenName: true,
        profilePicture: true,
      }
    });

    return NextResponse.json(linkedAccounts);
  } catch (error) {
    console.error("Failed to fetch linked Twitter accounts:", error);
    return NextResponse.json({ error: "Failed to fetch accounts." }, { status: 500 });
  }
} 