import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { validateAuth } from "@/lib/middleware/auth";

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const accountId = params.id;
    if (!accountId) {
      return NextResponse.json(
        { error: "Account ID is required" },
        { status: 400 }
      );
    }

    // Check if the account belongs to the user
    const account = await prisma.linkedAccount.findUnique({
      where: {
        id: accountId,
        userId: user.id,
        provider: 'twitter'
      }
    });

    if (!account) {
      return NextResponse.json(
        { error: "Twitter account not found or does not belong to the user" },
        { status: 404 }
      );
    }

    // Delete the account
    await prisma.linkedAccount.delete({
      where: { id: accountId }
    });

    return NextResponse.json({
      success: true,
      message: "Twitter account disconnected successfully"
    });
  } catch (error) {
    console.error("Failed to disconnect Twitter account:", error);
    return NextResponse.json(
      { error: "Failed to disconnect Twitter account" },
      { status: 500 }
    );
  }
}

