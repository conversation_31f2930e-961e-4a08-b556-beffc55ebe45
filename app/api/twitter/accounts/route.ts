import { NextRequest, NextResponse } from "next/server";
import { Twitter<PERSON><PERSON> } from "twitter-api-v2";
import { getIronSession, IronSession } from "iron-session";
import { cookies } from "next/headers";
import { sessionOptions, SessionData } from "@/lib/auth/session";
import { prisma } from "@/lib/prisma";
import { validateAuth } from "@/lib/middleware/auth";

export async function GET(request: NextRequest) {
  const session: IronSession<SessionData> = await getIronSession(cookies(), sessionOptions);
  
  if (!session.accessToken) {
    return NextResponse.json({ account: null });
  }

  try {
    const twitterClient = new TwitterApi(session.accessToken);
    const { data: user } = await twitterClient.v2.me({ "user.fields": ["profile_image_url"] });

    return NextResponse.json({
      account: {
        id: user.id,
        name: user.name,
        username: user.username,
        profile_image_url: user.profile_image_url,
      },
    });
  } catch (error) {
    console.error("Failed to fetch Twitter user:", error);
    session.destroy();
    return NextResponse.json({ account: null }, { status: 401 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const session = await getIronSession(cookies(), sessionOptions);
    session.destroy();

    await prisma.user.update({
        where: { id: user.id },
        data: { twitterAuthId: null },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to disconnect Twitter account:", error);
    return NextResponse.json({ error: "Failed to disconnect account." }, { status: 500 });
  }
} 