import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { validateAuth } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

const preferencesSchema = z.object({
  theme: z.enum(['light', 'dark', 'system']).optional(),
  timezone: z.string().optional(),
  language: z.string().optional(),
  emailNotifications: z.boolean().optional(),
  pushNotifications: z.boolean().optional(),
  marketingEmails: z.boolean().optional(),
  weeklyDigest: z.boolean().optional(),
  autoSave: z.boolean().optional(),
  defaultAiProvider: z.enum(['OPENAI', 'GOOGLE']).optional(),
});

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Validate authentication
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user preferences from database
    const userPreferences = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        preferences: true,
      }
    });

    if (!userPreferences) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Return user preferences with defaults
    const defaultPreferences = {
      theme: 'system',
      timezone: 'UTC',
      language: 'en',
      emailNotifications: true,
      pushNotifications: true,
      marketingEmails: false,
      weeklyDigest: true,
      autoSave: true,
    };

    const preferences = {
      ...defaultPreferences,
      ...(userPreferences.preferences as any || {}),
    };

    return NextResponse.json({
      preferences
    });

  } catch (error) {
    console.error('Error fetching user preferences:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest): Promise<NextResponse> {
  try {
    // Validate authentication
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = preferencesSchema.parse(body);

    // Get current preferences
    const currentUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        preferences: true,
        defaultAiProvider: true,
      }
    });

    if (!currentUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Merge with existing preferences
    const currentPreferences = (currentUser.preferences as any) || {};
    const updatedPreferences = {
      ...currentPreferences,
      ...validatedData,
    };

    // Update user preferences and AI provider if changed
    const updateData: any = {
      preferences: updatedPreferences,
      updatedAt: new Date(),
    };

    if (validatedData.defaultAiProvider && validatedData.defaultAiProvider !== currentUser.defaultAiProvider) {
      updateData.defaultAiProvider = validatedData.defaultAiProvider;
    }

    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: updateData,
      select: {
        preferences: true,
        defaultAiProvider: true,
      }
    });

    return NextResponse.json({
      message: 'Preferences updated successfully',
      preferences: updatedUser.preferences,
      defaultAiProvider: updatedUser.defaultAiProvider,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating user preferences:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
