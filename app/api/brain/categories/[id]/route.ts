import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { validateAuth } from "@/lib/middleware/auth";
import { BrainCategoryUpdate } from "@/lib/brain/types";

const categoryUpdateSchema = z.object({
  name: z.string().min(1).max(100),
});

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const auth = await validateAuth(req);
    if (!auth?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const category = await prisma.brainCategory.findUnique({
      where: { id: resolvedParams.id },
      include: {
        brainEntries: {
          orderBy: { createdAt: 'desc' },
          take: 10, // Include recent entries for preview
        },
        _count: {
          select: { brainEntries: true }
        }
      },
    });

    if (!category) {
      return new Response("Category not found", { status: 404 });
    }

    if (category.userId !== auth.id) {
      return new Response("Unauthorized", { status: 403 });
    }

    return Response.json(category);
  } catch (error) {
    console.error("Failed to fetch category:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const auth = await validateAuth(req);
    if (!auth?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const json = await req.json();
    const parsed = categoryUpdateSchema.safeParse(json);

    if (!parsed.success) {
      return new Response("Invalid request body", { status: 400 });
    }

    // Verify ownership
    const category = await prisma.brainCategory.findUnique({
      where: { id: resolvedParams.id },
    });

    if (!category) {
      return new Response("Category not found", { status: 404 });
    }

    if (category.userId !== auth.id) {
      return new Response("Unauthorized", { status: 403 });
    }

    const update: BrainCategoryUpdate = {
      id: resolvedParams.id,
      name: parsed.data.name,
    };

    const updated = await prisma.brainCategory.update({
      where: { id: resolvedParams.id },
      data: update,
    });

    return Response.json(updated);
  } catch (error) {
    console.error("Failed to update category:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const auth = await validateAuth(req);
    if (!auth?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    // Verify ownership
    const category = await prisma.brainCategory.findUnique({
      where: { id: resolvedParams.id },
    });

    if (!category) {
      return new Response("Category not found", { status: 404 });
    }

    if (category.userId !== auth.id) {
      return new Response("Unauthorized", { status: 403 });
    }

    // Delete the category
    await prisma.brainCategory.delete({
      where: { id: resolvedParams.id },
    });

    return new Response(null, { status: 204 });
  } catch (error) {
    console.error("Failed to delete category:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}