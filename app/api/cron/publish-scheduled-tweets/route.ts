import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { TweetStatus } from '@prisma/client';

// Retry configuration
const MAX_RETRIES = 3;
const RETRY_DELAYS = [1000, 5000, 15000]; // 1s, 5s, 15s (exponential backoff)

interface RetryableError {
  shouldRetry: boolean;
  delay: number;
}

function analyzeError(error: any, attempt: number): RetryableError {
  // Rate limit errors - retry with longer delay
  if (error?.code === 'HTTP_429' || error?.message?.includes('rate limit')) {
    return {
      shouldRetry: attempt < MAX_RETRIES,
      delay: Math.min(RETRY_DELAYS[attempt - 1] * 2, 60000) // Cap at 1 minute
    };
  }

  // Network/temporary errors - retry
  if (error?.code === 'REQUEST_ERROR' || error?.message?.includes('network')) {
    return {
      shouldRetry: attempt < MAX_RETRIES,
      delay: RETRY_DELAYS[attempt - 1] || 15000
    };
  }

  // Authentication errors - don't retry
  if (error?.code === 'HTTP_401' || error?.message?.includes('authentication')) {
    return { shouldRetry: false, delay: 0 };
  }

  // Other errors - retry with standard delay
  return {
    shouldRetry: attempt < MAX_RETRIES,
    delay: RETRY_DELAYS[attempt - 1] || 15000
  };
}

async function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function publishScheduledTweet(scheduledTweet: any): Promise<{ success: boolean; tweetId?: string; error?: string }> {
  let lastError: any;

  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      console.log(`Publishing tweet ${scheduledTweet.id}, attempt ${attempt}/${MAX_RETRIES}`);

      // Get media files if any
      const media = scheduledTweet.mediaUrlsJson
        ? JSON.parse(scheduledTweet.mediaUrlsJson as string).map((url: string) => ({
          file: { fileURL: url, fileType: 'image/jpeg' }, // Simplified for now
          twitterMediaId: undefined
        }))
        : undefined;

      // Lazy import to avoid circular dependency issues
      const { getTwitterPublisher } = await import('@/lib/tweets/publisher');
      const twitterPublisher = getTwitterPublisher();
      const result = await twitterPublisher.publish({
        content: scheduledTweet.content,
        twitterAccount: scheduledTweet.twitterAccount,
        media
      });

      if (result.success) {
        console.log(`Successfully published tweet ${scheduledTweet.id}: ${result.tweetId}`);
        return { success: true, tweetId: result.tweetId };
      } else {
        lastError = result.error;
        const retryInfo = analyzeError(result.error, attempt);

        if (!retryInfo.shouldRetry) {
          console.error(`Non-retryable error for tweet ${scheduledTweet.id}:`, result.error);
          return { success: false, error: result.error?.message || 'Unknown error' };
        }

        if (attempt < MAX_RETRIES) {
          console.log(`Retrying tweet ${scheduledTweet.id} in ${retryInfo.delay}ms...`);
          await sleep(retryInfo.delay);
        }
      }
    } catch (error) {
      lastError = error;
      const retryInfo = analyzeError(error, attempt);

      if (!retryInfo.shouldRetry) {
        console.error(`Non-retryable error for tweet ${scheduledTweet.id}:`, error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
      }

      if (attempt < MAX_RETRIES) {
        console.log(`Retrying tweet ${scheduledTweet.id} in ${retryInfo.delay}ms due to error:`, error);
        await sleep(retryInfo.delay);
      }
    }
  }

  console.error(`Failed to publish tweet ${scheduledTweet.id} after ${MAX_RETRIES} attempts:`, lastError);
  return {
    success: false,
    error: lastError instanceof Error ? lastError.message : 'Max retries exceeded'
  };
}

export async function POST(req: NextRequest) {
  try {
    console.log('Starting scheduled tweet publishing job...');

    // Get all pending scheduled tweets that are due
    const now = new Date();
    const scheduledTweets = await prisma.scheduledTweet.findMany({
      where: {
        status: TweetStatus.PENDING,
        scheduledAt: {
          lte: now
        }
      },
      include: {
        twitterAccount: true,
        user: true,
        botPersona: true
      },
      orderBy: {
        scheduledAt: 'asc'
      }
    });

    console.log(`Found ${scheduledTweets.length} tweets to publish`);

    if (scheduledTweets.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No tweets to publish',
        processed: 0
      });
    }

    const results = {
      published: 0,
      failed: 0,
      errors: [] as string[]
    };

    // Process each scheduled tweet
    for (const scheduledTweet of scheduledTweets) {
      try {
        const publishResult = await publishScheduledTweet(scheduledTweet);

        if (publishResult.success) {
          // Update status to PUBLISHED
          await prisma.scheduledTweet.update({
            where: { id: scheduledTweet.id },
            data: {
              status: TweetStatus.PUBLISHED,
              publishedTweetId: publishResult.tweetId,
              updatedAt: new Date()
            }
          });
          results.published++;
        } else {
          // Update status to FAILED
          await prisma.scheduledTweet.update({
            where: { id: scheduledTweet.id },
            data: {
              status: TweetStatus.FAILED,
              updatedAt: new Date()
            }
          });
          results.failed++;
          results.errors.push(`Tweet ${scheduledTweet.id}: ${publishResult.error}`);
        }
      } catch (error) {
        console.error(`Error processing scheduled tweet ${scheduledTweet.id}:`, error);

        // Update status to FAILED
        await prisma.scheduledTweet.update({
          where: { id: scheduledTweet.id },
          data: {
            status: TweetStatus.FAILED,
            updatedAt: new Date()
          }
        });

        results.failed++;
        results.errors.push(`Tweet ${scheduledTweet.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    console.log(`Scheduled tweet publishing completed. Published: ${results.published}, Failed: ${results.failed}`);

    return NextResponse.json({
      success: true,
      message: `Processed ${scheduledTweets.length} tweets`,
      results
    });

  } catch (error) {
    console.error('Error in scheduled tweet publishing job:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Allow GET requests for manual testing
export async function GET(req: NextRequest) {
  // Add a simple authentication check for manual testing
  const authHeader = req.headers.get('authorization');
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  return POST(req);
}
