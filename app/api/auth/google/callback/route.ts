import { NextRequest, NextResponse } from 'next/server';
import { generateAuthToken, setAuth<PERSON><PERSON>ie } from '@/lib/middleware/auth';
import { config } from '@/lib/config';
import { prisma } from '@/lib/prisma';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    // Handle OAuth errors
    if (error) {
      console.error('Google OAuth error:', error);
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Google authentication failed`);
    }

    // Validate state parameter
    if (state !== 'google_oauth') {
      console.error('Invalid state parameter');
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Invalid authentication state`);
    }

    if (!code) {
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=No authorization code received`);
    }

    // Exchange code for access token
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: config.auth.google.clientId,
        client_secret: config.auth.google.clientSecret,
        code,
        grant_type: 'authorization_code',
        redirect_uri: config.auth.google.callbackUrl,
      }),
    });

    if (!tokenResponse.ok) {
      console.error('Failed to exchange code for token');
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Failed to get access token`);
    }

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    // Get user profile from Google
    const profileResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!profileResponse.ok) {
      console.error('Failed to get user profile');
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Failed to get user profile`);
    }

    const profile = await profileResponse.json();

    // Find or create user in database
    let user = await prisma.user.findUnique({
      where: { email: profile.email },
    });

    if (!user) {
      // Create new user
      user = await prisma.user.create({
        data: {
          email: profile.email,
          name: profile.name,
          profilePicture: profile.picture,
          googleId: profile.id,
          emailVerified: true, // Google emails are pre-verified
        },
      });
    } else {
      // Update existing user with Google info if not already set
      user = await prisma.user.update({
        where: { id: user.id },
        data: {
          googleId: profile.id,
          profilePicture: user.profilePicture || profile.picture,
          name: user.name || profile.name,
        },
      });
    }

    // Generate JWT token
    const token = await generateAuthToken({
      id: user.id,
      email: user.email,
      name: user.name || undefined,
      image: user.profilePicture || undefined,
    });

    // Create response with successful redirect
    const response = NextResponse.redirect(`${config.app.baseUrl}/dashboard/home`);

    // Set auth cookie
    setAuthCookie(response, token);

    return response;
  } catch (error) {
    console.error('Google callback error:', error);
    return NextResponse.redirect(`${config.app.baseUrl}/login?error=Authentication failed`);
  }
}