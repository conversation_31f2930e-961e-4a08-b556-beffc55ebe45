import { NextRequest, NextResponse } from 'next/server';
import { config } from '@/lib/config';

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Build Google OAuth URL manually
    const googleAuthUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth');

    const params = {
      client_id: config.auth.google.clientId,
      redirect_uri: config.auth.google.callbackUrl,
      response_type: 'code',
      scope: 'profile email',
      access_type: 'offline',
      prompt: 'consent',
      state: 'google_oauth', // Add state for security
    };

    // Add parameters to URL
    Object.entries(params).forEach(([key, value]) => {
      googleAuthUrl.searchParams.append(key, value);
    });

    return NextResponse.redirect(googleAuthUrl.toString());
  } catch (error) {
    console.error('Google OAuth redirect error:', error);
    return NextResponse.json(
      { error: 'Failed to initiate Google OAuth' },
      { status: 500 }
    );
  }
}