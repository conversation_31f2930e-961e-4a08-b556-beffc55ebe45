import NextAuth, { AuthOptions, Session } from "next-auth";
import { JWT } from "next-auth/jwt";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import Twitter<PERSON>rovider from "next-auth/providers/twitter";
import { Adapter } from "next-auth/adapters";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { PrismaClient } from "@prisma/client";

import { prisma } from "@/lib/prisma";
import * as bcrypt from "bcryptjs";
import { encrypt } from "@/lib/crypto";

// For NextAuth.js with App Router, the configuration is in a single route file.
// See: https://next-auth.js.org/configuration/options

export const authOptions: AuthOptions = {
  adapter: PrismaAdapter(prisma) as Adapter,
  providers: [
    CredentialsProvider({
      // The name to display on the sign in form (e.g. "Sign in with...")
      name: "Credentials",
      // `credentials` is used to generate a form on the sign in page.
      // You can specify which fields should be submitted, by adding keys to the `credentials` object.
      // e.g. username, password, email, etc.
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required.");
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email }
        });

        if (!user || !user.hashedPassword) {
          throw new Error("No user found with this email.");
        }

        const isValidPassword = await bcrypt.compare(credentials.password, user.hashedPassword);

        if (!isValidPassword) {
          throw new Error("Incorrect password.");
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
        };
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID ?? "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET ?? "",
    }),
    TwitterProvider({
      clientId: process.env.TWITTER_CLIENT_ID ?? "",
      clientSecret: process.env.TWITTER_CLIENT_SECRET ?? "",
      version: "2.0", // Request OAuth 2.0 flow
    }),
    // TwitterProvider will be added later
  ],
  session: {
    strategy: "jwt", // Use JWT for stateless sessions
  },
  // Add callbacks here later (jwt, session, signIn)
  callbacks: {
    async jwt({ token, user }) {
      // When the user logs in for the first time `user` will be defined.
      // Persist the user id (and any other info) to the token so it can be
      // used in stateless JWT sessions.
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      // Forward the user id from the token to the session object so it is
      // easily accessible on the client.
      if (session.user && token.id) {
        (session.user as any).id = token.id;
      }
      return session;
    },
    async signIn({ user, account, profile }) {
      try {
        if (!user.id || !account) return false;

        const profileName = profile?.name;
        const profileImage = profile?.image;
        const twitterScreenName = account.provider === "twitter" ? (profile as any)?.screen_name : undefined;

        // For any OAuth provider, upsert the LinkedAccount
        await (prisma as PrismaClient).linkedAccount.upsert({
          where: {
            provider_providerAccountId: {
              provider: account.provider,
              providerAccountId: account.providerAccountId,
            }
          },
          create: {
            userId: user.id,
            provider: account.provider,
            providerAccountId: account.providerAccountId,
            accessToken: account.access_token
              ? encrypt(account.access_token)
              : null,
            refreshToken: account.refresh_token
              ? encrypt(account.refresh_token)
              : null,
            expiresAt: account.expires_at,
            // Add profile info for display
            name: profileName,
            profilePicture: profileImage,
            screenName: twitterScreenName,
          },
          update: {
            accessToken: account.access_token
              ? encrypt(account.access_token)
              : null,
            refreshToken: account.refresh_token
              ? encrypt(account.refresh_token)
              : null,
            expiresAt: account.expires_at,
            // Update profile info
            name: profileName,
            profilePicture: profileImage,
            screenName: twitterScreenName,
          },
        });

        // Also, ensure the user record is up-to-date with profile info
        if (account.provider === "google") {
          await prisma.user.update({
            where: { id: user.id },
            data: {
              name: user.name ?? profile?.name,
              profilePicture: user.image ?? (profile as any)?.picture,
              googleId: account.providerAccountId,
            },
          });
        } else if (account.provider === "twitter") {
          await prisma.user.update({
            where: { id: user.id },
            data: {
              name: user.name ?? profile?.name,
              profilePicture: user.image ?? (profile as any)?.profile_image_url,
              twitterId: account.providerAccountId,
            },
          });
        }
      } catch (error) {
        console.error("signIn callback error:", error);
        return false;
      }

      return true;
    },
  },
  // Add pages configuration later if needed (signIn, signOut, error, verifyRequest, newUser)
  // pages: {
  //   signIn: '/login', // Specify custom login page
  // },
  // Add secret here (from environment variable)
  secret: process.env.NEXTAUTH_SECRET,
  // Add debug logging in development
  debug: process.env.NODE_ENV === "development",
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
