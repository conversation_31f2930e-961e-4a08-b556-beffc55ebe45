import { NextRequest, NextResponse } from 'next/server';
import { config } from '@/lib/config';

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check if Twitter credentials are configured
    if (!config.auth.twitter.clientId || !config.auth.twitter.clientSecret) {
      console.error('Twitter OAuth credentials not configured');
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication not configured`);
    }

    // Lazy import to avoid circular dependency issues
    const { TwitterApi } = await import('twitter-api-v2');

    // Initialize Twitter client for OAuth 1.0a
    const client = new TwitterApi({
      appKey: config.auth.twitter.clientId,
      appSecret: config.auth.twitter.clientSecret,
    });

    // Generate OAuth URL
    const authLink = await client.generateAuthLink(config.auth.twitter.callbackUrl);

    // Store the oauth_token_secret in a way that can be retrieved later
    // For now, we'll redirect directly - in production you might want to store this in a session

    return NextResponse.redirect(authLink.url);
  } catch (error) {
    console.error('Twitter OAuth initiation error:', error);
    return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication failed`);
  }
}