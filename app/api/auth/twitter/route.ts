import { NextRequest, NextResponse } from 'next/server';
import { config } from '@/lib/config';

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check if Twitter credentials are configured
    if (!config.auth.twitter.clientId || !config.auth.twitter.clientSecret) {
      console.error('Twitter OAuth credentials not configured');
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication not configured`);
    }

    // Generate state parameter for security
    const state = Math.random().toString(36).substring(2, 15);

    // Generate code verifier and challenge for PKCE
    const codeVerifier = Math.random().toString(36).substring(2, 128);
    const codeChallenge = Buffer.from(codeVerifier).toString('base64url');

    // Build Twitter OAuth 2.0 URL
    const twitterAuthUrl = new URL('https://twitter.com/i/oauth2/authorize');

    const params = {
      response_type: 'code',
      client_id: config.auth.twitter.clientId,
      redirect_uri: config.auth.twitter.callbackUrl,
      scope: 'tweet.read tweet.write users.read offline.access',
      state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
    };

    // Add parameters to URL
    Object.entries(params).forEach(([key, value]) => {
      twitterAuthUrl.searchParams.append(key, value);
    });

    // TODO: Store state and code_verifier in session for callback verification
    // For now, we'll proceed without session storage

    return NextResponse.redirect(twitterAuthUrl.toString());
  } catch (error) {
    console.error('Twitter OAuth initiation error:', error);
    return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication failed`);
  }
}