import { NextRequest, NextResponse } from 'next/server';
import { generateAuthToken, setAuth<PERSON><PERSON>ie } from '@/lib/middleware/auth';
import { config } from '@/lib/config';
import { prisma } from '@/lib/prisma';
import { encrypt } from '@/lib/utils/encryption';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    if (error) {
      console.error('Twitter OAuth error:', error);
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication failed`);
    }

    if (!code) {
      console.error('Missing authorization code');
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication failed`);
    }

    // Check if Twitter credentials are configured
    if (!config.auth.twitter.clientId || !config.auth.twitter.clientSecret) {
      console.error('Twitter OAuth credentials not configured');
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication not configured`);
    }

    // TODO: Verify state parameter for security
    // For now, we'll proceed without state verification

    // Exchange authorization code for access token
    const tokenResponse = await fetch('https://api.twitter.com/2/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${config.auth.twitter.clientId}:${config.auth.twitter.clientSecret}`).toString('base64')}`,
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: config.auth.twitter.callbackUrl,
        // code_verifier would be needed here for PKCE, but we'll skip for now
      }),
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.text();
      console.error('Token exchange failed:', errorData);
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication failed`);
    }

    const tokenData = await tokenResponse.json();
    const { access_token, refresh_token } = tokenData;

    // Get user profile using the access token
    const profileResponse = await fetch('https://api.twitter.com/2/users/me?user.fields=profile_image_url,description,username', {
      headers: {
        'Authorization': `Bearer ${access_token}`,
      },
    });

    if (!profileResponse.ok) {
      console.error('Failed to fetch user profile');
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication failed`);
    }

    const profileData = await profileResponse.json();
    const profile = profileData.data;

    // Find or create user
    let user = await prisma.user.findFirst({
      where: { twitterId: profile.id },
    });

    if (!user) {
      user = await prisma.user.create({
        data: {
          email: `${profile.id}@twitter.user`, // Twitter OAuth 2.0 doesn't provide email by default
          twitterId: profile.id,
          name: profile.name,
          profilePicture: profile.profile_image_url,
          emailVerified: false,
        },
      });
    } else {
      // Update user with latest Twitter info
      await prisma.user.update({
        where: { id: user.id },
        data: {
          name: profile.name,
          profilePicture: profile.profile_image_url,
        },
      });
    }

    // Store Twitter credentials securely in LinkedAccount
    await prisma.linkedAccount.upsert({
      where: {
        provider_providerAccountId: {
          provider: 'twitter',
          providerAccountId: profile.id,
        }
      },
      create: {
        userId: user.id,
        provider: 'twitter',
        providerAccountId: profile.id,
        accessToken: encrypt(access_token),
        refreshToken: refresh_token ? encrypt(refresh_token) : null,
        name: profile.name,
        screenName: profile.username,
        profilePicture: profile.profile_image_url,
      },
      update: {
        accessToken: encrypt(access_token),
        refreshToken: refresh_token ? encrypt(refresh_token) : null,
        name: profile.name,
        screenName: profile.username,
        profilePicture: profile.profile_image_url,
      },
    });

    // Generate JWT
    const token = await generateAuthToken({
      id: user.id,
      email: user.email,
      name: user.name || undefined,
      image: user.profilePicture || undefined,
    });

    const response = NextResponse.redirect(`${config.app.baseUrl}/dashboard/home`);
    setAuthCookie(response, token);

    return response;
  } catch (error) {
    console.error('Twitter authentication error:', error);
    return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication failed`);
  }
}