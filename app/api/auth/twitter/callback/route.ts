import { NextRequest, NextResponse } from 'next/server';
import { generateAuthToken, setAuth<PERSON><PERSON>ie } from '@/lib/middleware/auth';
import { config } from '@/lib/config';
import { prisma } from '@/lib/prisma';
import { encrypt } from '@/lib/utils/encryption';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const oauth_token = searchParams.get('oauth_token');
    const oauth_verifier = searchParams.get('oauth_verifier');

    if (!oauth_token || !oauth_verifier) {
      return NextResponse.redirect(
        `${config.app.baseUrl}/login?error=Invalid Twitter callback parameters`
      );
    }

    // Check if Twitter credentials are configured
    if (!config.auth.twitter.clientId || !config.auth.twitter.clientSecret) {
      console.error('Twitter OAuth credentials not configured');
      return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication not configured`);
    }

    // Lazy import to avoid circular dependency issues
    const { TwitterApi } = await import('twitter-api-v2');

    // Initialize Twitter client with temporary credentials
    const client = new TwitterApi({
      appKey: config.auth.twitter.clientId,
      appSecret: config.auth.twitter.clientSecret,
      accessToken: oauth_token,
      accessSecret: '', // Will be determined
    });

    // Exchange temporary tokens for permanent ones
    const {
      client: loggedClient,
      accessToken,
      accessSecret,
      screenName,
      userId,
    } = await client.login(oauth_verifier);

    // Get user profile
    const { data: profile } = await loggedClient.v2.me({
      'user.fields': ['profile_image_url', 'description', 'email'] as any,
    });

    // Find or create user
    let user = await prisma.user.findFirst({
      where: { twitterId: userId },
    });

    if (!user) {
      user = await prisma.user.create({
        data: {
          email: (profile as any).email || `${userId}@twitter.user`,
          twitterId: userId,
          name: profile.name,
          profilePicture: profile.profile_image_url,
          emailVerified: !!(profile as any).email,
        },
      });
    } else {
      // Update user with latest Twitter info
      await prisma.user.update({
        where: { id: user.id },
        data: {
          name: profile.name,
          profilePicture: profile.profile_image_url,
        },
      });
    }

    // Store Twitter credentials securely
    const existingTwitterAccount = await prisma.twitterAccount.findFirst({
      where: { userId: user.id },
    });

    // Encrypt the tokens before storing
    const encryptedAccessToken = encrypt(accessToken);
    const encryptedRefreshToken = encrypt(accessSecret); // Twitter calls this accessSecret, but we store as refreshToken

    if (existingTwitterAccount) {
      await prisma.twitterAccount.update({
        where: { id: existingTwitterAccount.id },
        data: {
          twitterUserId: userId, // Correct field name from schema
          screenName: screenName,
          name: profile.name,
          profilePicture: profile.profile_image_url,
          accessToken: encryptedAccessToken,
          refreshToken: encryptedRefreshToken,
        },
      });
    } else {
      await prisma.twitterAccount.create({
        data: {
          userId: user.id,
          twitterUserId: userId, // Correct field name from schema
          screenName: screenName,
          name: profile.name,
          profilePicture: profile.profile_image_url,
          accessToken: encryptedAccessToken,
          refreshToken: encryptedRefreshToken,
        },
      });
    }

    // Generate JWT
    const token = await generateAuthToken({
      id: user.id,
      email: user.email,
      name: user.name || undefined,
      image: user.profilePicture || undefined,
    });

    const response = NextResponse.redirect(`${config.app.baseUrl}/dashboard/home`);
    setAuthCookie(response, token);

    return response;
  } catch (error) {
    console.error('Twitter authentication error:', error);
    return NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication failed`);
  }
}