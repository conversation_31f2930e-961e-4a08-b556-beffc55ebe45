import { NextRequest, NextResponse } from "next/server";
import { requireAuth } from "@/lib/middleware/auth";
import { prisma } from "@/lib/prisma";
import { PersonaService } from "@/lib/bots/persona-service";
import { z } from "zod";

// Initialize the persona service
const personaService = new PersonaService(prisma);

// Schema for validating persona update
const personaUpdateSchema = z.object({
  name: z.string().min(1, "Name is required").max(100).optional(),
  system: z.string().min(1, "System prompt is required").optional(),
  bio: z.array(z.string()).min(1, "At least one bio entry is required").optional(),
  lore: z.array(z.string()).optional(),
  messageExamples: z.array(z.any()).optional(),
  postExamples: z.array(z.string()).optional(),
  adjectives: z.array(z.string()).optional(),
  topics: z.array(z.string()).optional(),
  style: z.object({
    all: z.array(z.string()).optional(),
    chat: z.array(z.string()).optional(),
    post: z.array(z.string()).optional()
  }).optional(),
  version: z.number().optional()
});

/**
 * GET /api/bots/personas/[id]
 * Get a specific persona by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the authenticated user
    const authResult = await requireAuth(req);

    // Check if authentication failed
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const user = authResult;

    // Get the persona
    const persona = await personaService.getPersona(params.id);

    // Check if persona exists
    if (!persona) {
      return NextResponse.json(
        { error: "Persona not found" },
        { status: 404 }
      );
    }

    // Check if the persona belongs to the user
    if (persona.userId !== user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    return NextResponse.json({ persona });
  } catch (error) {
    console.error("Error getting persona:", error);
    return NextResponse.json(
      { error: "Failed to get persona" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/bots/personas/[id]
 * Update a specific persona by ID
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the authenticated user
    const authResult = await requireAuth(req);

    // Check if authentication failed
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const user = authResult;

    // Get the persona to check ownership
    const existingPersona = await personaService.getPersona(params.id);

    // Check if persona exists
    if (!existingPersona) {
      return NextResponse.json(
        { error: "Persona not found" },
        { status: 404 }
      );
    }

    // Check if the persona belongs to the user
    if (existingPersona.userId !== user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = personaUpdateSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid persona data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Update the persona
    const updatedPersona = await personaService.updatePersona(
      params.id,
      validationResult.data
    );

    return NextResponse.json({ persona: updatedPersona });
  } catch (error) {
    console.error("Error updating persona:", error);
    return NextResponse.json(
      { error: "Failed to update persona" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/bots/personas/[id]
 * Delete a specific persona by ID
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the authenticated user
    const authResult = await requireAuth(req);

    // Check if authentication failed
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const user = authResult;

    // Get the persona to check ownership
    const existingPersona = await personaService.getPersona(params.id);

    // Check if persona exists
    if (!existingPersona) {
      return NextResponse.json(
        { error: "Persona not found" },
        { status: 404 }
      );
    }

    // Check if the persona belongs to the user
    if (existingPersona.userId !== user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    // Delete the persona
    await personaService.deletePersona(params.id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting persona:", error);
    return NextResponse.json(
      { error: "Failed to delete persona" },
      { status: 500 }
    );
  }
}

