import { NextRequest, NextResponse } from "next/server";
import { requireAuth } from "@/lib/middleware/auth";
import { prisma } from "@/lib/prisma";
import { PersonaService } from "@/lib/bots/persona-service";

// Initialize the persona service
const personaService = new PersonaService(prisma);

/**
 * POST /api/bots/personas/[id]/active
 * Set a persona as active
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the authenticated user
    const authResult = await requireAuth(req);

    // Check if authentication failed
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const user = authResult;

    // Get the persona to check ownership
    const existingPersona = await personaService.getPersona(params.id);

    // Check if persona exists
    if (!existingPersona) {
      return NextResponse.json(
        { error: "Persona not found" },
        { status: 404 }
      );
    }

    // Check if the persona belongs to the user
    if (existingPersona.userId !== user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    // Set the persona as active
    const activePersona = await personaService.setActivePersona(
      user.id,
      params.id
    );

    return NextResponse.json({ persona: activePersona });
  } catch (error) {
    console.error("Error setting active persona:", error);
    return NextResponse.json(
      { error: "Failed to set active persona" },
      { status: 500 }
    );
  }
}

