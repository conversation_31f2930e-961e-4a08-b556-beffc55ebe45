import { NextRequest, NextResponse } from "next/server";
import { requireAuth } from "@/lib/middleware/auth";
import { prisma } from "@/lib/prisma";
import { PersonaService } from "@/lib/bots/persona-service";

// Initialize the persona service
const personaService = new PersonaService(prisma);

/**
 * GET /api/bots/personas/[id]/export
 * Export a persona to JSON
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the authenticated user
    const authResult = await requireAuth(req);

    // Check if authentication failed
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const user = authResult;

    // Get the persona to check ownership
    const existingPersona = await personaService.getPersona(params.id);

    // Check if persona exists
    if (!existingPersona) {
      return NextResponse.json(
        { error: "Persona not found" },
        { status: 404 }
      );
    }

    // Check if the persona belongs to the user
    if (existingPersona.userId !== user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const includeMetadata = url.searchParams.get("metadata") === "true";

    // Export the persona
    const result = await personaService.exportPersona(params.id, {
      includeMetadata
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || "Failed to export persona" },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: result.data });
  } catch (error) {
    console.error("Error exporting persona:", error);
    return NextResponse.json(
      { error: "Failed to export persona" },
      { status: 500 }
    );
  }
}

