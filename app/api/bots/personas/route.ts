import { NextRequest, NextResponse } from "next/server";
import { requireAuth } from "@/lib/middleware/auth";
import { prisma } from "@/lib/prisma";
import { PersonaService } from "@/lib/bots/persona-service";
import { PersonaSerializer } from "@/lib/bots/persona-serializer";
import { EnhancedPersonaTemplate } from "@/lib/bots/types";
import { z } from "zod";

// Initialize the persona service
const personaService = new PersonaService(prisma);

// Schema for validating persona creation/update
const personaSchema = z.object({
  name: z.string().min(1, "Name is required").max(100),
  system: z.string().min(1, "System prompt is required"),
  bio: z.array(z.string()).min(1, "At least one bio entry is required"),
  lore: z.array(z.string()).optional().default([]),
  messageExamples: z.array(z.any()).optional().default([]),
  postExamples: z.array(z.string()).optional().default([]),
  adjectives: z.array(z.string()).optional().default([]),
  topics: z.array(z.string()).optional().default([]),
  style: z.object({
    all: z.array(z.string()).optional().default([]),
    chat: z.array(z.string()).optional().default([]),
    post: z.array(z.string()).optional().default([])
  }).optional().default({
    all: [],
    chat: [],
    post: []
  }),
  version: z.number().optional()
});

/**
 * GET /api/bots/personas
 * Get all personas for the current user
 * Supports query parameters for filtering, sorting, and pagination:
 * - active: boolean - Get only the active persona
 * - search: string - Search term for filtering personas
 * - sort: string - Field to sort by (name, createdAt, updatedAt)
 * - order: string - Sort order (asc, desc)
 * - page: number - Page number (1-based)
 * - limit: number - Number of items per page
 */
export async function GET(req: NextRequest) {
  try {
    // Get the authenticated user
    const authResult = await requireAuth(req);

    // Check if authentication failed
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const user = authResult;

    // Get query parameters
    const url = new URL(req.url);
    const activeOnly = url.searchParams.get("active") === "true";
    const searchTerm = url.searchParams.get("search") || "";
    const sortField = url.searchParams.get("sort") || "updatedAt";
    const sortOrder = url.searchParams.get("order") || "desc";
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const limit = parseInt(url.searchParams.get("limit") || "20", 10);

    // Use the global persona service instance

    // Get personas
    let personas;
    let totalCount = 0;

    if (activeOnly) {
      const activePersona = await personaService.getActivePersona(user.id);
      personas = activePersona ? [activePersona] : [];
      totalCount = personas.length;
    } else {
      // Get personas with pagination, filtering, and sorting
      const result = await personaService.getUserPersonasWithFilters(
        user.id,
        {
          searchTerm,
          sortField: sortField as any,
          sortOrder: sortOrder as "asc" | "desc",
          page,
          limit
        }
      );

      personas = result.personas;
      totalCount = result.totalCount;
    }

    return NextResponse.json({
      personas,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error("Error getting personas:", error);
    return NextResponse.json(
      { error: "Failed to get personas" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/bots/personas
 * Create a new persona
 */
export async function POST(req: NextRequest) {
  try {
    // Get the authenticated user
    const authResult = await requireAuth(req);

    // Check if authentication failed
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const user = authResult;

    // Parse and validate request body
    const body = await req.json();
    const validationResult = personaSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid persona data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Create the persona
    const persona = await personaService.createPersona(
      user.id,
      validationResult.data as EnhancedPersonaTemplate
    );

    return NextResponse.json({ persona }, { status: 201 });
  } catch (error) {
    console.error("Error creating persona:", error);
    return NextResponse.json(
      { error: "Failed to create persona" },
      { status: 500 }
    );
  }
}
