import { NextRequest, NextResponse } from "next/server";
import { requireAuth } from "@/lib/middleware/auth";
import { prisma } from "@/lib/prisma";
import { PersonaService } from "@/lib/bots/persona-service";
import { z } from "zod";

// Initialize the persona service
const personaService = new PersonaService(prisma);

// Schema for validating import request
const importSchema = z.object({
  jsonData: z.string().min(1, "JSON data is required")
});

/**
 * POST /api/bots/personas/import
 * Import a persona from JSON
 */
export async function POST(req: NextRequest) {
  try {
    // Get the authenticated user
    const authResult = await requireAuth(req);

    // Check if authentication failed
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const user = authResult;

    // Parse and validate request body
    const body = await req.json();
    const validationResult = importSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Import the persona
    const result = await personaService.importPersona(
      user.id,
      validationResult.data.jsonData
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || "Failed to import persona" },
        { status: 400 }
      );
    }

    // Get the imported persona
    const persona = result.personaId
      ? await personaService.getPersona(result.personaId)
      : null;

    return NextResponse.json({ success: true, persona }, { status: 201 });
  } catch (error) {
    console.error("Error importing persona:", error);
    return NextResponse.json(
      { error: "Failed to import persona" },
      { status: 500 }
    );
  }
}

