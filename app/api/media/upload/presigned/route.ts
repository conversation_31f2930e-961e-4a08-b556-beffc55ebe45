import { NextRequest } from 'next/server';
import { authenticateUser, createAuthenticatedResponse, createErrorResponse } from '@/lib/auth';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// This endpoint provides information about UploadThing configuration
// The actual file uploads are handled by UploadThing at /api/uploadthing

export async function GET(req: NextRequest) {
  try {
    // Skip execution during build time
    if (process.env.NODE_ENV === 'production' && !req.headers.get('user-agent')) {
      return createErrorResponse('Not available during build', 503);
    }

    const user = await authenticateUser(req);

    return createAuthenticatedResponse({
      message: 'UploadThing is configured for file uploads',
      uploadEndpoint: '/api/uploadthing',
      allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'video/mp4'],
      maxFileSizes: {
        image: '4MB',
        video: '16MB'
      },
      maxFileCount: {
        image: 4,
        video: 1
      }
    });
  } catch (error) {
    console.error('Error handling upload info request:', error);
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    return createErrorResponse('Internal server error', 500);
  }
}