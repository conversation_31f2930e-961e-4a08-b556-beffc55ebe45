import { createUploadthing, type FileRouter } from "uploadthing/next";
import { UploadThingError } from "uploadthing/server";
import { NextRequest } from "next/server";
import { authenticateUser } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

const f = createUploadthing();

// FileRouter for your app, can contain multiple FileRoutes
export const ourFileRouter = {
  // Define as many FileRoutes as you like, each with a unique routeSlug
  tweetMediaUploader: f({
    image: { maxFileSize: "4MB", maxFileCount: 4 },
    video: { maxFileSize: "16MB", maxFileCount: 1 },
  })
    // Set permissions and file types for this FileRoute
    .middleware(async ({ req }) => {
      // This code runs on your server before upload
      const user = await authenticateUser(req as NextRequest);

      // If you throw, the user will not be able to upload
      if (!user) throw new UploadThingError("Unauthorized");

      // Whatever is returned here is accessible in onUploadComplete as `metadata`
      return { userId: user.id };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log("Upload complete for userId:", metadata.userId);
      console.log("file url", file.url);

      const uploadedFile = await prisma.uploadedFile.create({
        data: {
          userId: metadata.userId,
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
          fileURL: file.url,
        },
      });

      // !!! Whatever is returned here is sent to the clientside `onClientUploadComplete` callback
      // Convert Date to string for JSON serialization
      return {
        uploadedBy: metadata.userId,
        file: {
          id: uploadedFile.id,
          userId: uploadedFile.userId,
          fileName: uploadedFile.fileName,
          fileType: uploadedFile.fileType,
          fileSize: uploadedFile.fileSize,
          fileURL: uploadedFile.fileURL,
          createdAt: uploadedFile.createdAt.toISOString(),
        },
      };
    }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter; 